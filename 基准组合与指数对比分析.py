# -*- coding: utf-8 -*-
"""
基准组合与指数对比分析
对比基准组合与各种指数组合的定期支付概率和风险绩效指标

⚠️ 重要说明：本程序仅使用Wind数据库的真实数据进行分析

基准组合构成（更新）：
- 10% * 中证红利指数
- 5% * 黄金
- 5% * 港股通高股息精选指数
- 10% * 标普500指数
- 70% * 中证纯债债基指数

对比对象：
1. 基准组合（原配置）
2. 中证红利指数*30% + 中证纯债*70%
3. 黄金*30% + 中证纯债*70%
4. 港股通高股息精选*30% + 中证纯债*70%
5. 标普500指数*30% + 中证纯债*70%
6. 万得混合债券型二级指数
7. 万得偏债混合型基金指数

支付机制：
- 支付比例：(同期十年期国债收益率+1%)/12
- 起始支付日期：持有期满6个月后的首月月末
- 支付频率：每月末支付
- 分析期间：随后12个月
- 测算方式：按日滚动测算近15年数据

数据要求：
- 必须安装WindPy并有有效的Wind账户
- 需要网络连接以获取实时数据

作者：AI Assistant
日期：2025年1月27日
版本：5.0 (基准组合与指数对比版本)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import datetime as dt
from dateutil.relativedelta import relativedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['figure.figsize'] = (20, 16)

# 导入Wind API - 仅支持真实数据
try:
    from WindPy import *
    # 启动Wind API连接
    result = w.start()
    if result.ErrorCode == 0:
        print("✅ Wind API 连接成功")
    else:
        print(f"❌ Wind API 连接失败，错误码：{result.ErrorCode}")
        w = None
except ImportError:
    print("❌ 未安装WindPy库，无法获取真实数据")
    print("请运行：pip install WindPy")
    w = None
except Exception as e:
    print(f"❌ Wind API 初始化失败：{e}")
    w = None

class BenchmarkIndexComparison:
    """基准组合与指数对比分析类"""

    def __init__(self):
        # 更新后的基准组合构成
        self.benchmark_components = {
            '中证红利全收益': {'code': 'H00922.CSI', 'weight': 0.10},
            '黄金': {'code': 'AU9999.SGE', 'weight': 0.05},
            '港股通高股息精选': {'code': '930839.CSI', 'weight': 0.05},
            '标普500指数': {'code': 'SPX.GI', 'weight': 0.10},
            '中债总指数': {'code': 'CBA00101.CS', 'weight': 0.70}
        }

        # 对比对象配置
        self.comparison_objects = {
            '基准组合': {
                'type': 'benchmark',
                'components': self.benchmark_components
            },
            '中证红利*30%+中证纯债*70%': {
                'type': 'simple_mix',
                'components': {
                    '中证红利全收益': {'code': 'H00922.CSI', 'weight': 0.30},
                    '中债总指数': {'code': 'CBA00101.CS', 'weight': 0.70}
                }
            },
            '黄金*30%+中证纯债*70%': {
                'type': 'simple_mix',
                'components': {
                    '黄金': {'code': 'AU9999.SGE', 'weight': 0.30},
                    '中债总指数': {'code': 'CBA00101.CS', 'weight': 0.70}
                }
            },
            '港股通高股息*30%+中证纯债*70%': {
                'type': 'simple_mix',
                'components': {
                    '港股通高股息精选': {'code': '930839.CSI', 'weight': 0.30},
                    '中债总指数': {'code': 'CBA00101.CS', 'weight': 0.70}
                }
            },
            '标普500*30%+中证纯债*70%': {
                'type': 'simple_mix',
                'components': {
                    '标普500指数': {'code': 'SPX.GI', 'weight': 0.30},
                    '中债总指数': {'code': 'CBA00101.CS', 'weight': 0.70}
                }
            },
            '万得混合债券型二级指数': {
                'type': 'single_index',
                'code': '885007.WI'
            },
            '万得偏债混合型基金指数': {
                'type': 'single_index',
                'code': '885005.WI'
            }
        }

        self.holding_period = 6  # 持有期6个月
        self.analysis_period = 12  # 分析期12个月
        self.lookback_years = 15  # 回测15年

        # 十年期国债收益率代码
        self.treasury_10y_code = 'M1004261.WI'  # 中国十年期国债收益率
        self.default_treasury_rate = 0.03  # 默认国债收益率3%（当数据不可用时使用）
        self.additional_rate = 0.01  # 额外1%

        print(f"基准组合与指数对比分析")
        print(f"="*70)
        print(f"更新后的基准组合构成：")
        for name, info in self.benchmark_components.items():
            print(f"  {name}: {info['weight']:.0%} ({info['code']})")
        print(f"\n对比对象：")
        for name, info in self.comparison_objects.items():
            if info['type'] == 'single_index':
                print(f"  {name}: {info['code']}")
            else:
                print(f"  {name}: 组合配置")
        print(f"\n支付基础：十年期国债收益率 + 1%")
        print(f"回测期间：近{self.lookback_years}年")

    def get_treasury_yield_data(self, start_date, end_date):
        """获取十年期国债收益率数据"""
        if w is None:
            raise RuntimeError("Wind API未连接，无法获取真实数据")

        try:
            print("正在获取十年期国债收益率数据...")

            result = w.wsd(self.treasury_10y_code, "close", start_date, end_date, usedf=True)

            if isinstance(result, tuple) and len(result) >= 2:
                error_info = result[0]
                if hasattr(error_info, 'ErrorCode') and error_info.ErrorCode != 0:
                    raise RuntimeError(f"Wind API返回错误：错误码 {error_info.ErrorCode}")
                yield_data = result[1]
            else:
                raise RuntimeError("Wind API返回格式异常")

            if yield_data.empty:
                raise RuntimeError("十年期国债收益率数据为空")

            # 确保索引是DatetimeIndex
            if not isinstance(yield_data.index, pd.DatetimeIndex):
                yield_data.index = pd.to_datetime(yield_data.index)

            yield_data.columns = ['十年期国债收益率']

            # 检查数据质量
            if yield_data['十年期国债收益率'].isna().all():
                print("   警告：十年期国债收益率数据全部为空，使用默认值")
                yield_data['十年期国债收益率'] = self.default_treasury_rate
            else:
                # 将百分比转换为小数（如果需要）
                valid_data = yield_data['十年期国债收益率'].dropna()
                if len(valid_data) > 0 and valid_data.mean() > 1:
                    yield_data['十年期国债收益率'] = yield_data['十年期国债收益率'] / 100

                # 前向填充缺失值
                yield_data = yield_data.fillna(method='ffill')

                # 如果仍有缺失值，使用默认值
                if yield_data['十年期国债收益率'].isna().any():
                    yield_data['十年期国债收益率'] = yield_data['十年期国债收益率'].fillna(self.default_treasury_rate)

            print(f"✅ 十年期国债收益率数据获取成功")
            print(f"   数据期间：{yield_data.index[0].strftime('%Y-%m-%d')} 至 {yield_data.index[-1].strftime('%Y-%m-%d')}")
            print(f"   平均收益率：{yield_data['十年期国债收益率'].mean():.2%}")

            return yield_data

        except Exception as e:
            raise RuntimeError(f"获取十年期国债收益率数据失败：{e}")

    def get_index_data(self, code, name, start_date, end_date):
        """获取单个指数数据"""
        if w is None:
            raise RuntimeError("Wind API未连接，无法获取真实数据")

        try:
            print(f"  正在获取 {name} ({code}) 数据...")

            # 根据不同指数类型选择合适的字段
            if 'AU9999' in code:  # 黄金
                field = "close"
            elif '.HI' in code:  # 港股指数
                field = "close"
            elif '.GI' in code:  # 国际指数
                field = "close"
            else:  # A股指数和基金指数
                field = "close"

            result = w.wsd(code, field, start_date, end_date, usedf=True)

            if isinstance(result, tuple) and len(result) >= 2:
                error_info = result[0]
                if hasattr(error_info, 'ErrorCode') and error_info.ErrorCode != 0:
                    print(f"    警告：{name} 数据获取失败，错误码 {error_info.ErrorCode}")
                    return None
                data = result[1]
            else:
                print(f"    警告：{name} 数据返回格式异常")
                return None

            if data.empty:
                print(f"    警告：{name} 数据为空")
                return None

            # 确保索引是DatetimeIndex
            if not isinstance(data.index, pd.DatetimeIndex):
                data.index = pd.to_datetime(data.index)

            data.columns = [name]
            print(f"    ✅ {name} 数据获取成功，{len(data)}个数据点")

            return data[name]

        except Exception as e:
            print(f"    ❌ 获取{name}数据失败：{e}")
            return None

    def get_comparison_data(self, start_date, end_date):
        """获取所有对比对象的数据"""
        print("正在获取对比对象数据...")

        all_comparison_data = {}

        for obj_name, obj_config in self.comparison_objects.items():
            print(f"\n开始获取 {obj_name} 数据...")

            if obj_config['type'] == 'single_index':
                # 单一指数
                index_data = self.get_index_data(obj_config['code'], obj_name, start_date, end_date)
                if index_data is not None:
                    all_comparison_data[obj_name] = pd.DataFrame({'价格': index_data})
                else:
                    print(f"  ❌ {obj_name} 数据获取失败")

            else:
                # 组合配置
                component_data = {}
                for comp_name, comp_info in obj_config['components'].items():
                    comp_data = self.get_index_data(comp_info['code'], comp_name, start_date, end_date)
                    if comp_data is not None:
                        component_data[comp_name] = comp_data

                if component_data:
                    # 合并组合数据
                    combined_data = pd.DataFrame(component_data)

                    # 删除缺失值过多的日期
                    min_required_data = max(1, len(component_data) * 0.6)
                    combined_data = combined_data.dropna(thresh=min_required_data)

                    if not combined_data.empty:
                        # 前向填充缺失值
                        combined_data = combined_data.fillna(method='ffill')
                        combined_data = combined_data.dropna()

                        if not combined_data.empty:
                            all_comparison_data[obj_name] = combined_data
                            print(f"  ✅ {obj_name} 组合数据获取成功，{len(combined_data)}个数据点")
                        else:
                            print(f"  ❌ {obj_name} 填充后数据为空")
                    else:
                        print(f"  ❌ {obj_name} 合并后数据为空")
                else:
                    print(f"  ❌ {obj_name} 所有组件数据获取失败")

        return all_comparison_data

    def calculate_nav_data(self, price_data, obj_name, obj_config):
        """计算净值数据"""
        if obj_config['type'] == 'single_index':
            # 单一指数，直接计算收益率
            returns = price_data['价格'].pct_change().fillna(0)
            nav_series = (1 + returns).cumprod()

        else:
            # 组合配置，计算加权收益率
            returns_data = price_data.pct_change().fillna(0)

            # 计算加权组合收益率
            weighted_returns = pd.Series(0, index=returns_data.index)

            for comp_name, comp_info in obj_config['components'].items():
                if comp_name in returns_data.columns:
                    weighted_returns += returns_data[comp_name] * comp_info['weight']

            # 计算累积净值（以1为起始）
            nav_series = (1 + weighted_returns).cumprod()

        nav_data = pd.DataFrame({'净值': nav_series})

        print(f"  ✅ {obj_name} 净值计算完成")
        print(f"     起始净值：{nav_data.iloc[0]['净值']:.4f}")
        print(f"     最终净值：{nav_data.iloc[-1]['净值']:.4f}")
        print(f"     累计收益率：{(nav_data.iloc[-1]['净值'] / nav_data.iloc[0]['净值'] - 1):.2%}")

        return nav_data

    def calculate_risk_performance_metrics(self, nav_data, obj_name):
        """计算风险绩效指标"""
        # 计算日收益率
        returns = nav_data['净值'].pct_change().dropna()

        if len(returns) == 0:
            return {}

        # 计算年化收益率
        days = len(returns)
        years = days / 252  # 假设一年252个交易日
        total_return = nav_data['净值'].iloc[-1] / nav_data['净值'].iloc[0] - 1
        annual_return = (1 + total_return) ** (1/years) - 1 if years > 0 else 0

        # 计算年化波动率
        annual_volatility = returns.std() * np.sqrt(252)

        # 计算夏普比率（假设无风险利率为3%）
        risk_free_rate = 0.03
        sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility if annual_volatility > 0 else 0

        # 计算最大回撤
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()

        # 计算卡玛比率
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown < 0 else 0

        # 计算VaR (95%置信度)
        var_95 = returns.quantile(0.05)

        metrics = {
            '年化收益率': annual_return,
            '年化波动率': annual_volatility,
            '夏普比率': sharpe_ratio,
            '最大回撤': max_drawdown,
            '卡玛比率': calmar_ratio,
            'VaR(95%)': var_95,
            '累计收益率': total_return
        }

        return metrics

    def rolling_analysis(self, nav_data, treasury_yield_data, product_name):
        """按日滚动分析"""
        print(f"开始{product_name}的按日滚动分析...")

        # 计算最小需要的数据长度
        min_required_days = 18 * 30 + 60

        # 找到所有可能的投资起点日期
        possible_start_dates = []
        for date in nav_data.index:
            future_dates = nav_data.index[nav_data.index > date]
            if len(future_dates) >= min_required_days:
                possible_start_dates.append(date)

        if len(possible_start_dates) == 0:
            print(f"  ❌ {product_name}没有足够的历史数据进行完整分析")
            return []

        print(f"  有效投资起点数量：{len(possible_start_dates)}")
        print(f"  分析期间：{possible_start_dates[0].strftime('%Y-%m-%d')} 至 {possible_start_dates[-1].strftime('%Y-%m-%d')}")

        # 按日滚动分析每个投资起点
        all_results = []
        progress_step = max(1, len(possible_start_dates) // 10)

        for i, start_date in enumerate(possible_start_dates):
            if (i + 1) % progress_step == 0 or i == len(possible_start_dates) - 1:
                print(f"  完成分析：{i + 1}/{len(possible_start_dates)} ({(i+1)/len(possible_start_dates)*100:.1f}%)")

            try:
                scenario_result = self.simulate_payment_scenario(nav_data, treasury_yield_data, start_date, product_name)
                if not scenario_result.empty:
                    scenario_result['投资起点'] = start_date
                    all_results.append(scenario_result)
            except Exception:
                continue

        print(f"  ✅ {product_name}滚动分析完成，有效分析次数：{len(all_results)}")
        return all_results

    def analyze_probability(self, all_results, product_name):
        """分析使用本金的概率"""
        if not all_results:
            print(f"{product_name}没有有效的分析结果")
            return None

        # 统计每个支付期使用本金的概率
        period_probabilities = {}

        for period in range(1, self.analysis_period + 1):
            use_principal_count = 0
            total_count = 0

            for result in all_results:
                period_data = result[result['支付期数'] == period]
                if not period_data.empty:
                    total_count += 1
                    if period_data.iloc[0]['使用本金']:
                        use_principal_count += 1

            if total_count > 0:
                probability = use_principal_count / total_count
                period_probabilities[period] = {
                    '概率': probability,
                    '使用本金次数': use_principal_count,
                    '总次数': total_count
                }

        return period_probabilities

    def get_payment_rate(self, date, treasury_yield_data):
        """获取指定日期的支付率"""
        # 找到最接近的国债收益率数据
        available_dates = treasury_yield_data.index[treasury_yield_data.index <= date]
        if len(available_dates) == 0:
            treasury_rate = treasury_yield_data.iloc[0]['十年期国债收益率']
        else:
            current_date = available_dates[-1]
            treasury_rate = treasury_yield_data.loc[current_date]['十年期国债收益率']

        # 计算支付率：十年期国债收益率 + 1%
        annual_payment_rate = treasury_rate + self.additional_rate
        monthly_payment_rate = annual_payment_rate / 12

        return annual_payment_rate, monthly_payment_rate

    def calculate_payment_schedule(self, purchase_date):
        """计算支付时间表"""
        # 持有期满6个月后的首月月末
        holding_end = purchase_date + relativedelta(months=self.holding_period)
        first_payment_date = holding_end + relativedelta(day=31)

        # 生成12个月的支付日期
        payment_dates = []
        current_date = first_payment_date

        for _ in range(self.analysis_period):
            payment_dates.append(current_date)
            current_date = current_date + relativedelta(months=1, day=31)

        return payment_dates

    def simulate_payment_scenario(self, nav_data, treasury_yield_data, purchase_date, product_name, initial_investment=10000):
        """模拟单次投资的支付场景"""
        payment_dates = self.calculate_payment_schedule(purchase_date)

        # 获取购买日净值
        purchase_nav = nav_data.loc[nav_data.index >= purchase_date].iloc[0]['净值']
        shares = initial_investment / purchase_nav

        results = []
        cumulative_payment = 0

        for i, payment_date in enumerate(payment_dates):
            # 找到最接近支付日期的净值
            available_dates = nav_data.index[nav_data.index <= payment_date]
            if len(available_dates) == 0:
                continue

            current_date = available_dates[-1]
            current_nav = nav_data.loc[current_date]['净值']

            # 计算当前市值
            current_value = shares * current_nav
            cumulative_return = current_value - initial_investment

            # 获取当期支付率
            annual_rate, monthly_rate = self.get_payment_rate(current_date, treasury_yield_data)

            # 计算应支付金额
            payment_amount = initial_investment * monthly_rate
            cumulative_payment += payment_amount

            # 判断是否使用本金
            use_principal = cumulative_payment > cumulative_return

            results.append({
                '支付期数': i + 1,
                '支付日期': payment_date,
                '净值日期': current_date,
                '当前净值': current_nav,
                '当前市值': current_value,
                '累积收益': cumulative_return,
                '年化支付率': annual_rate,
                '月支付率': monthly_rate,
                '当期支付': payment_amount,
                '累积支付': cumulative_payment,
                '使用本金': use_principal,
                '本金使用金额': max(0, cumulative_payment - cumulative_return),
                '产品类型': product_name
            })

        return pd.DataFrame(results)

    def plot_comprehensive_comparison(self, all_nav_data, all_metrics, all_probs, save_path=None):
        """绘制综合对比图表"""
        print("正在生成综合对比图表...")

        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))

        # 找到统一起点
        latest_start_date = None
        for obj_name, nav_data in all_nav_data.items():
            if nav_data is not None and not nav_data.empty:
                start_date = nav_data.index[0]
                if latest_start_date is None or start_date > latest_start_date:
                    latest_start_date = start_date

        print(f"统一起点日期：{latest_start_date.strftime('%Y-%m-%d')}")

        # 子图1：净值走势对比（归一化）
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink']
        for i, (obj_name, nav_data) in enumerate(all_nav_data.items()):
            if nav_data is not None and not nav_data.empty:
                nav_aligned = nav_data[nav_data.index >= latest_start_date]
                if not nav_aligned.empty:
                    nav_normalized = nav_aligned['净值'] / nav_aligned['净值'].iloc[0]
                    ax1.plot(nav_aligned.index, nav_normalized,
                            linewidth=2, label=obj_name, color=colors[i % len(colors)], alpha=0.8)

        ax1.set_title(f'净值走势对比（统一起点：{latest_start_date.strftime("%Y-%m-%d")}，归一化）', fontsize=14, fontweight='bold')
        ax1.set_xlabel('日期')
        ax1.set_ylabel('相对净值（起点=1）')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 子图2：风险绩效指标对比
        if all_metrics:
            metrics_df = pd.DataFrame(all_metrics).T

            # 选择关键指标进行展示
            key_metrics = ['年化收益率', '年化波动率', '夏普比率', '最大回撤']
            available_metrics = [m for m in key_metrics if m in metrics_df.columns]

            if available_metrics:
                x = np.arange(len(metrics_df))
                width = 0.15

                for i, metric in enumerate(available_metrics):
                    values = metrics_df[metric].values
                    ax2.bar(x + i * width, values, width, label=metric, alpha=0.7)

                ax2.set_title('风险绩效指标对比', fontsize=14, fontweight='bold')
                ax2.set_xlabel('对比对象')
                ax2.set_ylabel('指标值')
                ax2.set_xticks(x + width * (len(available_metrics) - 1) / 2)
                ax2.set_xticklabels([name[:10] + '...' if len(name) > 10 else name for name in metrics_df.index],
                                   rotation=45, ha='right')
                ax2.legend()
                ax2.grid(True, alpha=0.3)

        # 子图3：支付风险概率对比
        if all_probs:
            periods = list(range(1, self.analysis_period + 1))

            for i, (obj_name, probs) in enumerate(all_probs.items()):
                if probs:
                    probabilities = [probs.get(p, {}).get('概率', 0) for p in periods]
                    ax3.plot(periods, probabilities, marker='o', linewidth=2,
                            label=obj_name, color=colors[i % len(colors)], markersize=4)

            ax3.set_title('各期使用本金概率对比', fontsize=14, fontweight='bold')
            ax3.set_xlabel('支付期数')
            ax3.set_ylabel('使用本金概率')
            ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            ax3.grid(True, alpha=0.3)
            ax3.set_ylim(0, 1)

        # 子图4：综合评分雷达图
        if all_metrics and all_probs:
            # 计算综合评分
            scores = {}
            for obj_name in all_metrics.keys():
                if obj_name in all_probs and all_probs[obj_name]:
                    # 收益评分（年化收益率）
                    return_score = min(all_metrics[obj_name].get('年化收益率', 0) * 10, 10)

                    # 风险评分（基于波动率，越低越好）
                    volatility = all_metrics[obj_name].get('年化波动率', 0.2)
                    risk_score = max(0, 10 - volatility * 50)

                    # 支付风险评分（基于平均使用本金概率，越低越好）
                    avg_prob = np.mean([all_probs[obj_name].get(p, {}).get('概率', 0) for p in range(1, 13)])
                    payment_score = max(0, 10 - avg_prob * 20)

                    # 夏普比率评分
                    sharpe_score = min(max(all_metrics[obj_name].get('夏普比率', 0) * 5, 0), 10)

                    scores[obj_name] = {
                        '收益': return_score,
                        '风险控制': risk_score,
                        '支付安全': payment_score,
                        '风险调整收益': sharpe_score
                    }

            if scores:
                # 绘制雷达图
                categories = list(next(iter(scores.values())).keys())
                N = len(categories)

                angles = [n / float(N) * 2 * np.pi for n in range(N)]
                angles += angles[:1]

                ax4 = plt.subplot(2, 2, 4, projection='polar')

                for i, (obj_name, score_dict) in enumerate(scores.items()):
                    values = list(score_dict.values())
                    values += values[:1]

                    ax4.plot(angles, values, 'o-', linewidth=2,
                            label=obj_name, color=colors[i % len(colors)])
                    ax4.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])

                ax4.set_xticks(angles[:-1])
                ax4.set_xticklabels(categories)
                ax4.set_ylim(0, 10)
                ax4.set_title('综合评分雷达图', fontsize=14, fontweight='bold', pad=20)
                ax4.legend(bbox_to_anchor=(1.3, 1.0))

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"综合对比图表已保存至：{save_path}")
            plt.close()
        else:
            plt.show()

def main():
    """主函数 - 基准组合与指数对比分析"""
    print("基准组合与指数对比分析")
    print("="*70)
    print("注意：本程序仅使用Wind数据库的真实数据进行分析")
    print("支付基础：十年期国债收益率 + 1%")

    try:
        # 创建分析实例
        analyzer = BenchmarkIndexComparison()

        # 设置分析期间（近15年）
        end_date = dt.datetime.now().strftime('%Y-%m-%d')
        start_date = (dt.datetime.now() - relativedelta(years=analyzer.lookback_years)).strftime('%Y-%m-%d')

        print(f"\n数据获取期间：{start_date} 至 {end_date}")

        # 获取十年期国债收益率数据
        treasury_yield_data = analyzer.get_treasury_yield_data(start_date, end_date)

        # 获取所有对比对象的数据
        all_comparison_data = analyzer.get_comparison_data(start_date, end_date)

        # 计算净值数据和风险绩效指标
        all_nav_data = {}
        all_metrics = {}
        all_results = {}
        all_probs = {}

        for obj_name, price_data in all_comparison_data.items():
            print(f"\n开始分析 {obj_name}...")

            # 计算净值
            obj_config = analyzer.comparison_objects[obj_name]
            nav_data = analyzer.calculate_nav_data(price_data, obj_name, obj_config)
            all_nav_data[obj_name] = nav_data

            # 计算风险绩效指标
            metrics = analyzer.calculate_risk_performance_metrics(nav_data, obj_name)
            all_metrics[obj_name] = metrics

            # 进行支付概率分析
            results = analyzer.rolling_analysis(nav_data, treasury_yield_data, obj_name)
            probs = analyzer.analyze_probability(results, obj_name)

            all_results[obj_name] = results
            all_probs[obj_name] = probs

        # 生成对比报告
        print("\n" + "="*80)
        print("基准组合与指数对比分析报告")
        print("="*80)

        print(f"\n风险绩效指标对比：")
        print("-" * 100)
        print(f"{'对比对象':>20} {'年化收益率':>12} {'年化波动率':>12} {'夏普比率':>10} {'最大回撤':>10} {'累计收益率':>12}")
        print("-" * 100)

        for obj_name, metrics in all_metrics.items():
            if metrics:
                print(f"{obj_name:>20} {metrics.get('年化收益率', 0):>11.2%} {metrics.get('年化波动率', 0):>11.2%} "
                      f"{metrics.get('夏普比率', 0):>9.2f} {metrics.get('最大回撤', 0):>9.2%} {metrics.get('累计收益率', 0):>11.2%}")

        print(f"\n支付风险概率对比：")
        print("-" * 100)
        print(f"{'对比对象':>20} {'任意一期使用本金':>15} {'平均使用本金概率':>15} {'最高期概率':>12}")
        print("-" * 100)

        for obj_name, probs in all_probs.items():
            if probs:
                # 计算任意一期使用本金概率（简化计算）
                max_prob = max([probs.get(p, {}).get('概率', 0) for p in range(1, 13)])
                avg_prob = np.mean([probs.get(p, {}).get('概率', 0) for p in range(1, 13)])

                print(f"{obj_name:>20} {max_prob:>14.2%} {avg_prob:>14.2%} {max_prob:>11.2%}")

        # 生成综合对比图表
        chart_path = f"基准组合与指数综合对比图_{dt.datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        analyzer.plot_comprehensive_comparison(all_nav_data, all_metrics, all_probs, save_path=chart_path)

        # 保存详细结果
        filename = f"基准组合与指数对比分析结果_{dt.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 风险绩效指标汇总
            if all_metrics:
                metrics_df = pd.DataFrame(all_metrics).T
                metrics_df.to_excel(writer, sheet_name='风险绩效指标')

            # 各对象概率汇总
            for obj_name, probs in all_probs.items():
                if probs:
                    prob_df = pd.DataFrame.from_dict(probs, orient='index')
                    prob_df.index.name = '支付期数'
                    # 清理sheet名称，移除特殊字符
                    clean_name = obj_name.replace('*', '').replace('%', '').replace('+', '').replace('/', '')
                    sheet_name = clean_name[:20] + "概率" if len(clean_name) > 20 else clean_name + "概率"
                    prob_df.to_excel(writer, sheet_name=sheet_name)

            # 对比配置信息
            config_data = []
            for obj_name, obj_config in analyzer.comparison_objects.items():
                if obj_config['type'] == 'single_index':
                    config_data.append({
                        '对比对象': obj_name,
                        '类型': '单一指数',
                        '代码': obj_config['code'],
                        '配置': '-'
                    })
                else:
                    components = []
                    for comp_name, comp_info in obj_config['components'].items():
                        components.append(f"{comp_name}({comp_info['weight']:.0%})")
                    config_data.append({
                        '对比对象': obj_name,
                        '类型': '组合配置',
                        '代码': '-',
                        '配置': ' + '.join(components)
                    })

            config_df = pd.DataFrame(config_data)
            config_df.to_excel(writer, sheet_name='配置信息', index=False)

        print(f"\n✅ 基准组合与指数对比分析完成！")
        print(f"📊 综合对比图：{chart_path}")
        print(f"📋 详细结果：{filename}")
        print(f"📈 数据来源：Wind数据库真实数据")
        print(f"💰 支付基础：十年期国债收益率 + 1%")
        print(f"🎯 对比对象：{len(all_nav_data)}个")

    except RuntimeError as e:
        print(f"\n❌ 程序执行失败：{e}")
        print("\n解决方案：")
        print("1. 确保已安装WindPy：pip install WindPy")
        print("2. 确保Wind终端已登录且账户有效")
        print("3. 检查网络连接")
        print("4. 验证指数代码是否正确")
        return

    except Exception as e:
        print(f"\n❌ 程序执行过程中发生未知错误：{e}")
        print("请检查程序代码或联系技术支持")
        return

if __name__ == "__main__":
    main()
