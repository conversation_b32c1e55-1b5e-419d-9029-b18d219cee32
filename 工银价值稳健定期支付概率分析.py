# -*- coding: utf-8 -*-
"""
工银价值稳健基金定期支付机制概率分析
计算在定期支付机制下使用本金而不是全部收益的概率

⚠️ 重要说明：本程序仅使用Wind数据库的真实基金净值数据进行分析，不使用任何模拟数据

支付机制：
- 支付比例：(大行理财利率+1%)/12
- 起始支付日期：持有期满6个月后的首月月末
- 支付频率：每月末支付
- 分析期间：随后12个月

数据要求：
- 必须安装WindPy并有有效的Wind账户
- 需要网络连接以获取实时数据
- 基金代码：013300.OF (工银价值稳健6个月持有混合FOF)

作者：AI Assistant
日期：2025年1月27日
版本：2.0 (仅真实数据版本)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import datetime as dt
from dateutil.relativedelta import relativedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['figure.figsize'] = (12, 8)

# 导入Wind API - 仅支持真实数据
try:
    from WindPy import *
    # 启动Wind API连接
    result = w.start()
    if result.ErrorCode == 0:
        print("✅ Wind API 连接成功")
    else:
        print(f"❌ Wind API 连接失败，错误码：{result.ErrorCode}")
        w = None
except ImportError:
    print("❌ 未安装WindPy库，无法获取真实数据")
    print("请运行：pip install WindPy")
    w = None
except Exception as e:
    print(f"❌ Wind API 初始化失败：{e}")
    w = None

class FundPaymentAnalysis:
    """工银价值稳健基金定期支付分析类"""

    def __init__(self):
        self.fund_code = '013300.OF'  # 工银价值稳健6个月持有混合(FOF)
        self.fund_name = '工银价值稳健6个月持有混合(FOF)'
        self.setup_date = '2021-11-09'  # 基金成立日期
        self.holding_period = 6  # 持有期6个月
        self.analysis_period = 12  # 分析期12个月

        # 大行理财利率假设（年化）
        self.bank_rate = 0.025  # 2.5%，可根据实际情况调整
        self.additional_rate = 0.01  # 额外1%
        self.total_rate = self.bank_rate + self.additional_rate  # 总支付率
        self.monthly_payment_rate = self.total_rate / 12  # 月支付率

        print(f"基金代码：{self.fund_code}")
        print(f"基金名称：{self.fund_name}")
        print(f"年化支付率：{self.total_rate:.2%}")
        print(f"月支付率：{self.monthly_payment_rate:.4%}")

    def get_fund_data(self, start_date, end_date):
        """获取基金净值数据 - 仅使用真实数据"""
        if w is None:
            raise RuntimeError("Wind API未连接，无法获取真实基金数据。请确保已安装WindPy并有有效的Wind账户。")

        try:
            print("正在从Wind数据库获取基金净值数据...")

            # 获取基金净值数据
            result = w.wsd(self.fund_code, "NAV_adj", start_date, end_date, usedf=True)

            # 检查Wind API返回结果
            if isinstance(result, tuple) and len(result) >= 2:
                error_info = result[0]
                if hasattr(error_info, 'ErrorCode') and error_info.ErrorCode != 0:
                    raise RuntimeError(f"Wind API返回错误：错误码 {error_info.ErrorCode}")
                nav_data = result[1]
            else:
                raise RuntimeError("Wind API返回格式异常")
            nav_data.columns = ['净值']
            nav_data = nav_data.dropna()

            if nav_data.empty:
                raise RuntimeError("获取的基金净值数据为空")

            # 确保索引是DatetimeIndex
            if not isinstance(nav_data.index, pd.DatetimeIndex):
                nav_data.index = pd.to_datetime(nav_data.index)

            # 获取基金成立以来的数据用于历史分析
            print("正在获取基金成立以来的完整数据...")
            full_result = w.wsd(self.fund_code, "NAV_adj", self.setup_date, end_date, usedf=True)

            # 检查Wind API返回结果
            if isinstance(full_result, tuple) and len(full_result) >= 2:
                error_info = full_result[0]
                if hasattr(error_info, 'ErrorCode') and error_info.ErrorCode != 0:
                    raise RuntimeError(f"Wind API返回错误：错误码 {error_info.ErrorCode}")
                full_data = full_result[1]
            else:
                raise RuntimeError("Wind API返回格式异常")
            full_data.columns = ['净值']
            full_data = full_data.dropna()

            if full_data.empty:
                raise RuntimeError("获取的完整基金数据为空")

            # 确保索引是DatetimeIndex
            if not isinstance(full_data.index, pd.DatetimeIndex):
                full_data.index = pd.to_datetime(full_data.index)

            print(f"成功获取基金真实数据，数据期间：{nav_data.index[0].strftime('%Y-%m-%d')} 至 {nav_data.index[-1].strftime('%Y-%m-%d')}")
            print(f"完整数据期间：{full_data.index[0].strftime('%Y-%m-%d')} 至 {full_data.index[-1].strftime('%Y-%m-%d')}")
            print(f"数据点数量：{len(full_data)}个交易日")

            return nav_data, full_data

        except Exception as e:
            raise RuntimeError(f"获取Wind真实数据失败：{e}。请检查网络连接、Wind账户状态和基金代码是否正确。")



    def calculate_payment_schedule(self, purchase_date):
        """计算支付时间表"""
        # 持有期满6个月后的首月月末
        holding_end = purchase_date + relativedelta(months=self.holding_period)
        first_payment_date = holding_end + relativedelta(day=31)  # 当月最后一天

        # 生成12个月的支付日期
        payment_dates = []
        current_date = first_payment_date

        for i in range(self.analysis_period):
            payment_dates.append(current_date)
            current_date = current_date + relativedelta(months=1, day=31)

        return payment_dates

    def simulate_payment_scenario(self, nav_data, purchase_date, initial_investment=10000):
        """模拟单次投资的支付场景"""
        payment_dates = self.calculate_payment_schedule(purchase_date)

        # 获取购买日净值
        purchase_nav = nav_data.loc[nav_data.index >= purchase_date].iloc[0]['净值']
        shares = initial_investment / purchase_nav  # 持有份额

        results = []
        cumulative_payment = 0
        cumulative_return = 0

        for i, payment_date in enumerate(payment_dates):
            # 找到最接近支付日期的净值
            available_dates = nav_data.index[nav_data.index <= payment_date]
            if len(available_dates) == 0:
                continue

            current_date = available_dates[-1]
            current_nav = nav_data.loc[current_date]['净值']

            # 计算当前市值
            current_value = shares * current_nav

            # 计算累积收益
            cumulative_return = current_value - initial_investment

            # 计算应支付金额
            payment_amount = initial_investment * self.monthly_payment_rate
            cumulative_payment += payment_amount

            # 判断是否使用本金
            use_principal = cumulative_payment > cumulative_return

            results.append({
                '支付期数': i + 1,
                '支付日期': payment_date,
                '净值日期': current_date,
                '当前净值': current_nav,
                '当前市值': current_value,
                '累积收益': cumulative_return,
                '当期支付': payment_amount,
                '累积支付': cumulative_payment,
                '使用本金': use_principal,
                '本金使用金额': max(0, cumulative_payment - cumulative_return)
            })

        return pd.DataFrame(results)

    def monte_carlo_analysis(self, nav_data, num_simulations=1000):
        """蒙特卡洛模拟分析"""
        print(f"开始蒙特卡洛模拟，模拟次数：{num_simulations}")

        # 可能的购买日期（基金成立后的每个交易日）
        setup_date_ts = pd.to_datetime(self.setup_date)
        possible_purchase_dates = nav_data.index[nav_data.index >= setup_date_ts]

        # 确保有足够的数据进行分析
        min_required_days = (self.holding_period + self.analysis_period) * 30
        valid_purchase_dates = []

        for date in possible_purchase_dates:
            future_data = nav_data.index[nav_data.index > date]
            if len(future_data) >= min_required_days:
                valid_purchase_dates.append(date)

        if len(valid_purchase_dates) == 0:
            print("警告：没有足够的历史数据进行完整分析")
            return None

        print(f"有效购买日期数量：{len(valid_purchase_dates)}")

        # 随机选择购买日期进行模拟
        np.random.seed(42)
        selected_dates = np.random.choice(valid_purchase_dates,
                                        size=min(num_simulations, len(valid_purchase_dates)),
                                        replace=True)

        simulation_results = []

        for i, purchase_date in enumerate(selected_dates):
            if (i + 1) % 100 == 0:
                print(f"完成模拟：{i + 1}/{len(selected_dates)}")

            try:
                scenario_result = self.simulate_payment_scenario(nav_data, purchase_date)
                if not scenario_result.empty:
                    simulation_results.append(scenario_result)
            except Exception as e:
                print(f"模拟第{i+1}次时出错：{e}")
                continue

        return simulation_results

    def analyze_probability(self, simulation_results):
        """分析使用本金的概率"""
        if not simulation_results:
            print("没有有效的模拟结果")
            return None

        print(f"有效模拟次数：{len(simulation_results)}")

        # 统计每个支付期使用本金的概率
        period_probabilities = {}

        for period in range(1, self.analysis_period + 1):
            use_principal_count = 0
            total_count = 0

            for result in simulation_results:
                period_data = result[result['支付期数'] == period]
                if not period_data.empty:
                    total_count += 1
                    if period_data.iloc[0]['使用本金']:
                        use_principal_count += 1

            if total_count > 0:
                probability = use_principal_count / total_count
                period_probabilities[period] = {
                    '概率': probability,
                    '使用本金次数': use_principal_count,
                    '总次数': total_count
                }

        return period_probabilities

    def generate_report(self, period_probabilities, simulation_results):
        """生成分析报告"""
        print("\n" + "="*60)
        print("工银价值稳健基金定期支付机制概率分析报告")
        print("="*60)

        print(f"\n基金信息：")
        print(f"  基金名称：{self.fund_name}")
        print(f"  基金代码：{self.fund_code}")
        print(f"  年化支付率：{self.total_rate:.2%} (大行理财利率{self.bank_rate:.2%} + 1%)")
        print(f"  月支付率：{self.monthly_payment_rate:.4%}")

        print(f"\n支付机制：")
        print(f"  持有期：{self.holding_period}个月")
        print(f"  支付开始：持有期满后的首月月末")
        print(f"  支付频率：每月末")
        print(f"  分析期间：{self.analysis_period}个月")

        if period_probabilities:
            print(f"\n各期使用本金概率：")
            print("-" * 40)
            for period, data in period_probabilities.items():
                print(f"第{period:2d}期：{data['概率']:6.2%} ({data['使用本金次数']:3d}/{data['总次数']:3d})")

            # 计算整体统计
            overall_stats = self._calculate_overall_stats(simulation_results)
            print(f"\n整体统计：")
            print("-" * 40)
            print(f"任意一期使用本金的概率：{overall_stats['any_period_prob']:.2%}")
            print(f"所有期都使用本金的概率：{overall_stats['all_periods_prob']:.2%}")
            print(f"平均使用本金期数：{overall_stats['avg_periods']:.1f}期")
            print(f"平均本金使用金额：{overall_stats['avg_principal_used']:.2f}元")

        print("\n" + "="*60)

        return period_probabilities

    def _calculate_overall_stats(self, simulation_results):
        """计算整体统计数据"""
        any_period_count = 0
        all_periods_count = 0
        total_simulations = len(simulation_results)
        total_periods_used = 0
        total_principal_used = 0

        for result in simulation_results:
            # 检查是否有任意一期使用本金
            if result['使用本金'].any():
                any_period_count += 1

            # 检查是否所有期都使用本金
            if result['使用本金'].all():
                all_periods_count += 1

            # 统计使用本金的期数
            periods_used = result['使用本金'].sum()
            total_periods_used += periods_used

            # 统计本金使用金额
            max_principal_used = result['本金使用金额'].max()
            total_principal_used += max_principal_used

        return {
            'any_period_prob': any_period_count / total_simulations,
            'all_periods_prob': all_periods_count / total_simulations,
            'avg_periods': total_periods_used / total_simulations,
            'avg_principal_used': total_principal_used / total_simulations
        }

    def plot_probability_chart(self, period_probabilities, save_path=None):
        """绘制概率图表"""
        if not period_probabilities:
            print("没有数据可供绘图")
            return

        periods = list(period_probabilities.keys())
        probabilities = [period_probabilities[p]['概率'] for p in periods]

        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

        # 子图1：各期使用本金概率
        bars = ax1.bar(periods, probabilities, color='lightcoral', alpha=0.7, edgecolor='darkred')
        ax1.set_xlabel('支付期数')
        ax1.set_ylabel('使用本金概率')
        ax1.set_title('各期使用本金概率分布')
        ax1.set_ylim(0, 1)
        ax1.grid(True, alpha=0.3)

        # 在柱状图上添加数值标签
        for bar, prob in zip(bars, probabilities):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{prob:.1%}', ha='center', va='bottom', fontsize=9)

        # 子图2：累积概率
        cumulative_probs = []
        for i in range(len(probabilities)):
            # 计算至少在前i+1期中有一期使用本金的概率
            prob_none = 1
            for j in range(i+1):
                prob_none *= (1 - probabilities[j])
            cumulative_probs.append(1 - prob_none)

        ax2.plot(periods, cumulative_probs, marker='o', linewidth=2, markersize=6, color='steelblue')
        ax2.set_xlabel('支付期数')
        ax2.set_ylabel('累积概率')
        ax2.set_title('至少一期使用本金的累积概率')
        ax2.set_ylim(0, 1)
        ax2.grid(True, alpha=0.3)

        # 在折线图上添加数值标签
        for i, (period, prob) in enumerate(zip(periods, cumulative_probs)):
            if i % 2 == 0:  # 每隔一个点显示标签，避免拥挤
                ax2.text(period, prob + 0.02, f'{prob:.1%}',
                        ha='center', va='bottom', fontsize=9)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存至：{save_path}")
            plt.close()  # 关闭图表，避免显示
        else:
            plt.show()

    def save_detailed_results(self, simulation_results, period_probabilities, filename=None):
        """保存详细结果到Excel文件"""
        if filename is None:
            filename = f"工银价值稳健定期支付分析结果_{dt.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 概率汇总表
            if period_probabilities:
                prob_df = pd.DataFrame.from_dict(period_probabilities, orient='index')
                prob_df.index.name = '支付期数'
                prob_df.to_excel(writer, sheet_name='概率汇总')

            # 详细模拟结果（取前10个样本）
            for i, result in enumerate(simulation_results[:10]):
                sheet_name = f'模拟样本{i+1}'
                result.to_excel(writer, sheet_name=sheet_name, index=False)

            # 参数设置
            params_df = pd.DataFrame({
                '参数': ['基金代码', '基金名称', '大行理财利率', '额外利率', '总支付率', '月支付率',
                        '持有期(月)', '分析期(月)', '模拟次数'],
                '值': [self.fund_code, self.fund_name, f'{self.bank_rate:.2%}',
                      f'{self.additional_rate:.2%}', f'{self.total_rate:.2%}',
                      f'{self.monthly_payment_rate:.4%}', self.holding_period,
                      self.analysis_period, len(simulation_results)]
            })
            params_df.to_excel(writer, sheet_name='参数设置', index=False)

        print(f"详细结果已保存至：{filename}")
        return filename

def main():
    """主函数 - 仅使用真实数据进行分析"""
    print("工银价值稳健基金定期支付机制概率分析")
    print("="*50)
    print("注意：本程序仅使用Wind数据库的真实基金数据进行分析")

    try:
        # 创建分析实例
        analyzer = FundPaymentAnalysis()

        # 设置分析期间
        end_date = dt.datetime.now().strftime('%Y-%m-%d')
        start_date = analyzer.setup_date

        print(f"\n数据获取期间：{start_date} 至 {end_date}")

        # 获取基金数据
        nav_data, full_data = analyzer.get_fund_data(start_date, end_date)

        print(f"基金数据获取完成，共{len(nav_data)}个交易日")

        # 验证数据质量
        if len(full_data) < 500:  # 至少需要500个交易日的数据
            print(f"警告：数据量较少（{len(full_data)}个交易日），分析结果可能不够稳定")

        # 进行蒙特卡洛模拟
        print("\n开始基于真实数据的蒙特卡洛模拟...")
        simulation_results = analyzer.monte_carlo_analysis(full_data, num_simulations=500)

        if not simulation_results:
            print("模拟失败，程序退出")
            return

        # 分析概率
        period_probabilities = analyzer.analyze_probability(simulation_results)

        # 生成报告
        analyzer.generate_report(period_probabilities, simulation_results)

        # 绘制图表
        chart_path = f"工银价值稳健概率分析图_{dt.datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        analyzer.plot_probability_chart(period_probabilities, save_path=chart_path)

        # 保存详细结果
        excel_path = analyzer.save_detailed_results(simulation_results, period_probabilities)

        print(f"\n✅ 分析完成！基于真实数据的分析结果：")
        print(f"📊 图表文件：{chart_path}")
        print(f"📋 Excel文件：{excel_path}")
        print(f"📈 数据来源：Wind数据库真实基金净值")
        print(f"📅 数据期间：{full_data.index[0].strftime('%Y-%m-%d')} 至 {full_data.index[-1].strftime('%Y-%m-%d')}")

    except RuntimeError as e:
        print(f"\n❌ 程序执行失败：{e}")
        print("\n解决方案：")
        print("1. 确保已安装WindPy：pip install WindPy")
        print("2. 确保Wind终端已登录且账户有效")
        print("3. 检查网络连接")
        print("4. 验证基金代码是否正确")
        return

    except Exception as e:
        print(f"\n❌ 程序执行过程中发生未知错误：{e}")
        print("请检查程序代码或联系技术支持")
        return

if __name__ == "__main__":
    main()
