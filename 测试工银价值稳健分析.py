# -*- coding: utf-8 -*-
"""
工银价值稳健基金定期支付机制概率分析 - 测试版本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import datetime as dt
from dateutil.relativedelta import relativedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def test_analysis():
    """测试分析功能"""
    print("工银价值稳健基金定期支付机制概率分析 - 测试版本")
    print("="*60)
    
    # 基金基本信息
    fund_code = '013300.OF'
    fund_name = '工银价值稳健6个月持有混合(FOF)'
    setup_date = '2021-11-09'
    
    # 支付参数
    bank_rate = 0.025  # 大行理财利率2.5%
    additional_rate = 0.01  # 额外1%
    total_rate = bank_rate + additional_rate  # 总支付率3.5%
    monthly_payment_rate = total_rate / 12  # 月支付率
    
    print(f"基金代码：{fund_code}")
    print(f"基金名称：{fund_name}")
    print(f"年化支付率：{total_rate:.2%}")
    print(f"月支付率：{monthly_payment_rate:.4%}")
    
    # 生成模拟数据
    print("\n生成模拟基金净值数据...")
    start_date = pd.to_datetime(setup_date)
    end_date = pd.to_datetime('2025-01-27')
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # 模拟基金净值走势
    np.random.seed(42)
    annual_return = 0.04  # 年化收益率4%
    annual_volatility = 0.08  # 年化波动率8%
    daily_return = annual_return / 252
    daily_volatility = annual_volatility / np.sqrt(252)
    
    returns = np.random.normal(daily_return, daily_volatility, len(dates))
    nav_values = [1.0]
    for ret in returns[1:]:
        nav_values.append(nav_values[-1] * (1 + ret))
    
    nav_data = pd.DataFrame({'净值': nav_values}, index=dates)
    print(f"模拟数据生成完成，期间：{dates[0].strftime('%Y-%m-%d')} 至 {dates[-1].strftime('%Y-%m-%d')}")
    
    # 模拟投资场景
    print("\n开始模拟投资场景...")
    
    # 选择几个不同的购买日期进行测试
    test_purchase_dates = [
        pd.to_datetime('2022-01-01'),
        pd.to_datetime('2022-06-01'),
        pd.to_datetime('2023-01-01'),
        pd.to_datetime('2023-06-01'),
        pd.to_datetime('2024-01-01')
    ]
    
    results_summary = []
    
    for purchase_date in test_purchase_dates:
        if purchase_date not in nav_data.index:
            # 找到最近的交易日
            available_dates = nav_data.index[nav_data.index >= purchase_date]
            if len(available_dates) == 0:
                continue
            purchase_date = available_dates[0]
        
        print(f"\n分析购买日期：{purchase_date.strftime('%Y-%m-%d')}")
        
        # 计算支付时间表
        holding_end = purchase_date + relativedelta(months=6)
        first_payment_date = holding_end + relativedelta(day=31)
        
        payment_dates = []
        current_date = first_payment_date
        for i in range(12):
            payment_dates.append(current_date)
            current_date = current_date + relativedelta(months=1, day=31)
        
        # 模拟支付过程
        initial_investment = 10000
        purchase_nav = nav_data.loc[purchase_date]['净值']
        shares = initial_investment / purchase_nav
        
        scenario_results = []
        cumulative_payment = 0
        
        for i, payment_date in enumerate(payment_dates):
            # 找到最接近支付日期的净值
            available_dates = nav_data.index[nav_data.index <= payment_date]
            if len(available_dates) == 0:
                continue
            
            current_date = available_dates[-1]
            current_nav = nav_data.loc[current_date]['净值']
            current_value = shares * current_nav
            cumulative_return = current_value - initial_investment
            
            payment_amount = initial_investment * monthly_payment_rate
            cumulative_payment += payment_amount
            
            use_principal = cumulative_payment > cumulative_return
            
            scenario_results.append({
                '期数': i + 1,
                '支付日期': payment_date.strftime('%Y-%m-%d'),
                '当前净值': current_nav,
                '累积收益': cumulative_return,
                '累积支付': cumulative_payment,
                '使用本金': use_principal
            })
        
        # 统计该场景下使用本金的期数
        use_principal_periods = sum(1 for r in scenario_results if r['使用本金'])
        results_summary.append({
            '购买日期': purchase_date.strftime('%Y-%m-%d'),
            '使用本金期数': use_principal_periods,
            '总期数': len(scenario_results),
            '使用本金比例': use_principal_periods / len(scenario_results) if scenario_results else 0
        })
        
        print(f"  使用本金期数：{use_principal_periods}/{len(scenario_results)}")
        print(f"  使用本金比例：{use_principal_periods / len(scenario_results):.1%}")
    
    # 汇总结果
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)
    
    summary_df = pd.DataFrame(results_summary)
    print(summary_df.to_string(index=False))
    
    if not summary_df.empty:
        avg_use_principal_ratio = summary_df['使用本金比例'].mean()
        print(f"\n平均使用本金比例：{avg_use_principal_ratio:.1%}")
        
        # 简单的概率估计
        any_period_prob = (summary_df['使用本金期数'] > 0).mean()
        all_periods_prob = (summary_df['使用本金期数'] == 12).mean()
        
        print(f"任意一期使用本金的概率：{any_period_prob:.1%}")
        print(f"所有期都使用本金的概率：{all_periods_prob:.1%}")
    
    # 绘制简单图表
    if not summary_df.empty:
        plt.figure(figsize=(10, 6))
        plt.bar(range(len(summary_df)), summary_df['使用本金比例'], 
                color='lightcoral', alpha=0.7, edgecolor='darkred')
        plt.xlabel('测试场景')
        plt.ylabel('使用本金比例')
        plt.title('不同购买时点的使用本金比例')
        plt.xticks(range(len(summary_df)), 
                  [d.split('-')[0] + '-' + d.split('-')[1] for d in summary_df['购买日期']], 
                  rotation=45)
        plt.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, ratio in enumerate(summary_df['使用本金比例']):
            plt.text(i, ratio + 0.01, f'{ratio:.1%}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.show()
    
    print("\n" + "="*60)
    print("测试完成！")
    print("\n说明：")
    print("1. 本测试使用模拟数据，实际结果可能有所不同")
    print("2. 大行理财利率假设为2.5%，实际利率可能变化")
    print("3. 基金收益假设年化4%，波动率8%")
    print("4. 完整分析请运行主程序进行蒙特卡洛模拟")

if __name__ == "__main__":
    test_analysis()
