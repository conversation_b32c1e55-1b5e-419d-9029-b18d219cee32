# -*- coding: utf-8 -*-
"""
综合定期支付机制概率分析
同时分析单只基金和基准组合的定期支付概率，并进行对比

⚠️ 重要说明：本程序仅使用Wind数据库的真实数据进行分析

基准组合构成：
- 10% * 中证红利指数
- 5% * 黄金
- 5% * 港股高股息指数
- 10% * 标普500指数
- 70% * 中证纯债债基指数

单只基金：
- 工银价值稳健6个月持有混合(FOF) - 013300.OF

支付机制：
- 支付比例：(同期十年期国债收益率+1%)/12
- 起始支付日期：持有期满6个月后的首月月末
- 支付频率：每月末支付
- 分析期间：随后12个月
- 测算方式：按日滚动测算近15年数据

数据要求：
- 必须安装WindPy并有有效的Wind账户
- 需要网络连接以获取实时数据

作者：AI Assistant
日期：2025年1月27日
版本：3.0 (综合分析版本)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import datetime as dt
from dateutil.relativedelta import relativedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['figure.figsize'] = (16, 12)

# 导入Wind API - 仅支持真实数据
try:
    from WindPy import *
    # 启动Wind API连接
    result = w.start()
    if result.ErrorCode == 0:
        print("✅ Wind API 连接成功")
    else:
        print(f"❌ Wind API 连接失败，错误码：{result.ErrorCode}")
        w = None
except ImportError:
    print("❌ 未安装WindPy库，无法获取真实数据")
    print("请运行：pip install WindPy")
    w = None
except Exception as e:
    print(f"❌ Wind API 初始化失败：{e}")
    w = None

class ComprehensivePaymentAnalysis:
    """综合定期支付分析类"""

    def __init__(self):
        # 基准组合构成
        self.benchmark_components = {
            '中证红利全收益': {'code': 'H00922.CSI', 'weight': 0.10},
            '黄金': {'code': 'AU9999.SGE', 'weight': 0.05},
            '恒生指数': {'code': 'HSI.HI', 'weight': 0.05},
            '标普500指数': {'code': 'SPX.GI', 'weight': 0.10},
            '中债总指数': {'code': 'CBA00101.CS', 'weight': 0.70}
        }

        # 单只基金信息
        self.fund_code = '485111.OF'  # 工银瑞信双利债券A
        self.fund_name = '工银瑞信双利债券A'
        self.fund_setup_date = '2009-05-27'  # 基金成立日期

        self.holding_period = 6  # 持有期6个月
        self.analysis_period = 12  # 分析期12个月
        self.lookback_years = 15  # 回测15年

        # 十年期国债收益率代码
        self.treasury_10y_code = 'M1004261.WI'  # 中国十年期国债收益率
        self.default_treasury_rate = 0.03  # 默认国债收益率3%（当数据不可用时使用）
        self.additional_rate = 0.01  # 额外1%

        print(f"综合定期支付机制概率分析")
        print(f"="*60)
        print(f"基准组合构成：")
        for name, info in self.benchmark_components.items():
            print(f"  {name}: {info['weight']:.0%} ({info['code']})")
        print(f"单只基金：{self.fund_name} ({self.fund_code})")
        print(f"支付基础：十年期国债收益率 + 1%")
        print(f"回测期间：近{self.lookback_years}年")

    def get_treasury_yield_data(self, start_date, end_date):
        """获取十年期国债收益率数据"""
        if w is None:
            raise RuntimeError("Wind API未连接，无法获取真实数据")

        try:
            print("正在获取十年期国债收益率数据...")

            result = w.wsd(self.treasury_10y_code, "close", start_date, end_date, usedf=True)

            if isinstance(result, tuple) and len(result) >= 2:
                error_info = result[0]
                if hasattr(error_info, 'ErrorCode') and error_info.ErrorCode != 0:
                    raise RuntimeError(f"Wind API返回错误：错误码 {error_info.ErrorCode}")
                yield_data = result[1]
            else:
                raise RuntimeError("Wind API返回格式异常")

            if yield_data.empty:
                raise RuntimeError("十年期国债收益率数据为空")

            # 确保索引是DatetimeIndex
            if not isinstance(yield_data.index, pd.DatetimeIndex):
                yield_data.index = pd.to_datetime(yield_data.index)

            yield_data.columns = ['十年期国债收益率']

            # 检查数据质量
            if yield_data['十年期国债收益率'].isna().all():
                print("   警告：十年期国债收益率数据全部为空，使用默认值")
                # 创建默认收益率数据
                yield_data['十年期国债收益率'] = self.default_treasury_rate
            else:
                # 将百分比转换为小数（如果需要）
                valid_data = yield_data['十年期国债收益率'].dropna()
                if len(valid_data) > 0 and valid_data.mean() > 1:
                    yield_data['十年期国债收益率'] = yield_data['十年期国债收益率'] / 100

                # 前向填充缺失值
                yield_data = yield_data.fillna(method='ffill')

                # 如果仍有缺失值，使用默认值
                if yield_data['十年期国债收益率'].isna().any():
                    yield_data['十年期国债收益率'] = yield_data['十年期国债收益率'].fillna(self.default_treasury_rate)

            print(f"✅ 十年期国债收益率数据获取成功")
            print(f"   数据期间：{yield_data.index[0].strftime('%Y-%m-%d')} 至 {yield_data.index[-1].strftime('%Y-%m-%d')}")
            print(f"   平均收益率：{yield_data['十年期国债收益率'].mean():.2%}")

            return yield_data

        except Exception as e:
            raise RuntimeError(f"获取十年期国债收益率数据失败：{e}")

    def get_fund_data(self, start_date, end_date):
        """获取基金净值数据"""
        if w is None:
            raise RuntimeError("Wind API未连接，无法获取真实数据")

        try:
            print("正在获取基金净值数据...")

            result = w.wsd(self.fund_code, "NAV_adj", start_date, end_date, usedf=True)

            if isinstance(result, tuple) and len(result) >= 2:
                error_info = result[0]
                if hasattr(error_info, 'ErrorCode') and error_info.ErrorCode != 0:
                    raise RuntimeError(f"Wind API返回错误：错误码 {error_info.ErrorCode}")
                nav_data = result[1]
            else:
                raise RuntimeError("Wind API返回格式异常")

            if nav_data.empty:
                raise RuntimeError("基金净值数据为空")

            # 确保索引是DatetimeIndex
            if not isinstance(nav_data.index, pd.DatetimeIndex):
                nav_data.index = pd.to_datetime(nav_data.index)

            nav_data.columns = ['净值']
            nav_data = nav_data.dropna()

            print(f"✅ 基金净值数据获取成功")
            print(f"   数据期间：{nav_data.index[0].strftime('%Y-%m-%d')} 至 {nav_data.index[-1].strftime('%Y-%m-%d')}")

            return nav_data

        except Exception as e:
            raise RuntimeError(f"获取基金数据失败：{e}")

    def get_benchmark_data(self, start_date, end_date):
        """获取基准组合数据"""
        if w is None:
            raise RuntimeError("Wind API未连接，无法获取真实数据")

        try:
            print("正在获取基准组合数据...")

            all_data = {}

            for name, info in self.benchmark_components.items():
                print(f"  正在获取 {name} ({info['code']}) 数据...")

                # 根据不同指数类型选择合适的字段
                if 'AU9999' in info['code']:  # 黄金
                    field = "close"
                elif '.HI' in info['code']:  # 港股指数
                    field = "close"
                elif '.GI' in info['code']:  # 国际指数
                    field = "close"
                else:  # A股指数
                    field = "close"

                result = w.wsd(info['code'], field, start_date, end_date, usedf=True)

                if isinstance(result, tuple) and len(result) >= 2:
                    error_info = result[0]
                    if hasattr(error_info, 'ErrorCode') and error_info.ErrorCode != 0:
                        print(f"    警告：{name} 数据获取失败，错误码 {error_info.ErrorCode}")
                        continue
                    data = result[1]
                else:
                    print(f"    警告：{name} 数据返回格式异常")
                    continue

                if data.empty:
                    print(f"    警告：{name} 数据为空")
                    continue

                # 确保索引是DatetimeIndex
                if not isinstance(data.index, pd.DatetimeIndex):
                    data.index = pd.to_datetime(data.index)

                data.columns = [name]
                all_data[name] = data[name]
                print(f"    ✅ {name} 数据获取成功，{len(data)}个数据点")

            if not all_data:
                raise RuntimeError("所有指数数据获取失败")

            # 合并所有数据
            combined_data = pd.DataFrame(all_data)

            # 删除缺失值过多的日期
            min_required_data = max(3, len(self.benchmark_components) * 0.6)
            combined_data = combined_data.dropna(thresh=min_required_data)

            if combined_data.empty:
                raise RuntimeError("合并后的数据为空")

            # 前向填充缺失值
            combined_data = combined_data.fillna(method='ffill')
            combined_data = combined_data.dropna()

            if combined_data.empty:
                raise RuntimeError("填充缺失值后数据为空")

            print(f"✅ 基准组合数据获取完成")
            print(f"   数据期间：{combined_data.index[0].strftime('%Y-%m-%d')} 至 {combined_data.index[-1].strftime('%Y-%m-%d')}")
            print(f"   数据点数量：{len(combined_data)}个交易日")

            return combined_data

        except Exception as e:
            raise RuntimeError(f"获取基准组合数据失败：{e}")

    def calculate_benchmark_nav(self, price_data):
        """计算基准组合净值"""
        print("正在计算基准组合净值...")

        # 计算各指数的日收益率
        returns_data = price_data.pct_change().fillna(0)

        # 计算加权组合收益率
        weighted_returns = pd.Series(0, index=returns_data.index)

        for name, info in self.benchmark_components.items():
            if name in returns_data.columns:
                weighted_returns += returns_data[name] * info['weight']

        # 计算累积净值（以1为起始）
        nav_series = (1 + weighted_returns).cumprod()
        nav_data = pd.DataFrame({'净值': nav_series})

        print(f"✅ 基准组合净值计算完成")
        print(f"   起始净值：{nav_data.iloc[0]['净值']:.4f}")
        print(f"   最终净值：{nav_data.iloc[-1]['净值']:.4f}")
        print(f"   累计收益率：{(nav_data.iloc[-1]['净值'] / nav_data.iloc[0]['净值'] - 1):.2%}")

        return nav_data

    def get_payment_rate(self, date, treasury_yield_data):
        """获取指定日期的支付率"""
        # 找到最接近的国债收益率数据
        available_dates = treasury_yield_data.index[treasury_yield_data.index <= date]
        if len(available_dates) == 0:
            # 如果没有历史数据，使用第一个可用数据
            treasury_rate = treasury_yield_data.iloc[0]['十年期国债收益率']
        else:
            current_date = available_dates[-1]
            treasury_rate = treasury_yield_data.loc[current_date]['十年期国债收益率']

        # 计算支付率：十年期国债收益率 + 1%
        annual_payment_rate = treasury_rate + self.additional_rate
        monthly_payment_rate = annual_payment_rate / 12

        return annual_payment_rate, monthly_payment_rate

    def calculate_payment_schedule(self, purchase_date):
        """计算支付时间表"""
        # 持有期满6个月后的首月月末
        holding_end = purchase_date + relativedelta(months=self.holding_period)
        first_payment_date = holding_end + relativedelta(day=31)  # 当月最后一天

        # 生成12个月的支付日期
        payment_dates = []
        current_date = first_payment_date

        for _ in range(self.analysis_period):
            payment_dates.append(current_date)
            current_date = current_date + relativedelta(months=1, day=31)

        return payment_dates

    def simulate_payment_scenario(self, nav_data, treasury_yield_data, purchase_date, product_name, initial_investment=10000):
        """模拟单次投资的支付场景"""
        payment_dates = self.calculate_payment_schedule(purchase_date)

        # 获取购买日净值
        purchase_nav = nav_data.loc[nav_data.index >= purchase_date].iloc[0]['净值']
        shares = initial_investment / purchase_nav  # 持有份额

        results = []
        cumulative_payment = 0

        for i, payment_date in enumerate(payment_dates):
            # 找到最接近支付日期的净值
            available_dates = nav_data.index[nav_data.index <= payment_date]
            if len(available_dates) == 0:
                continue

            current_date = available_dates[-1]
            current_nav = nav_data.loc[current_date]['净值']

            # 计算当前市值
            current_value = shares * current_nav

            # 计算累积收益
            cumulative_return = current_value - initial_investment

            # 获取当期支付率
            annual_rate, monthly_rate = self.get_payment_rate(current_date, treasury_yield_data)

            # 计算应支付金额
            payment_amount = initial_investment * monthly_rate
            cumulative_payment += payment_amount

            # 判断是否使用本金
            use_principal = cumulative_payment > cumulative_return

            results.append({
                '支付期数': i + 1,
                '支付日期': payment_date,
                '净值日期': current_date,
                '当前净值': current_nav,
                '当前市值': current_value,
                '累积收益': cumulative_return,
                '年化支付率': annual_rate,
                '月支付率': monthly_rate,
                '当期支付': payment_amount,
                '累积支付': cumulative_payment,
                '使用本金': use_principal,
                '本金使用金额': max(0, cumulative_payment - cumulative_return),
                '产品类型': product_name
            })

        return pd.DataFrame(results)

    def rolling_analysis(self, nav_data, treasury_yield_data, product_name):
        """按日滚动分析"""
        print(f"开始{product_name}的按日滚动分析...")

        # 计算最小需要的数据长度
        min_required_days = 18 * 30 + 60  # 18个月 + 60天缓冲

        # 找到所有可能的投资起点日期
        possible_start_dates = []
        for date in nav_data.index:
            # 检查从该日期开始是否有足够的未来数据
            future_dates = nav_data.index[nav_data.index > date]
            if len(future_dates) >= min_required_days:
                possible_start_dates.append(date)

        if len(possible_start_dates) == 0:
            raise RuntimeError(f"{product_name}没有足够的历史数据进行完整分析")

        print(f"  有效投资起点数量：{len(possible_start_dates)}")
        print(f"  分析期间：{possible_start_dates[0].strftime('%Y-%m-%d')} 至 {possible_start_dates[-1].strftime('%Y-%m-%d')}")

        # 按日滚动分析每个投资起点
        all_results = []
        progress_step = max(1, len(possible_start_dates) // 20)

        for i, start_date in enumerate(possible_start_dates):
            if (i + 1) % progress_step == 0 or i == len(possible_start_dates) - 1:
                print(f"  完成分析：{i + 1}/{len(possible_start_dates)} ({(i+1)/len(possible_start_dates)*100:.1f}%)")

            try:
                scenario_result = self.simulate_payment_scenario(nav_data, treasury_yield_data, start_date, product_name)
                if not scenario_result.empty:
                    scenario_result['投资起点'] = start_date
                    holding_end_date = start_date + relativedelta(months=self.holding_period)
                    scenario_result['持有期结束'] = holding_end_date
                    all_results.append(scenario_result)
            except Exception:
                continue

        print(f"  ✅ {product_name}滚动分析完成，有效分析次数：{len(all_results)}")
        return all_results

    def analyze_probability(self, all_results, product_name):
        """分析使用本金的概率"""
        if not all_results:
            print(f"{product_name}没有有效的分析结果")
            return None

        print(f"正在分析{product_name}概率，总样本数：{len(all_results)}")

        # 统计每个支付期使用本金的概率
        period_probabilities = {}

        for period in range(1, self.analysis_period + 1):
            use_principal_count = 0
            total_count = 0

            for result in all_results:
                period_data = result[result['支付期数'] == period]
                if not period_data.empty:
                    total_count += 1
                    if period_data.iloc[0]['使用本金']:
                        use_principal_count += 1

            if total_count > 0:
                probability = use_principal_count / total_count
                period_probabilities[period] = {
                    '概率': probability,
                    '使用本金次数': use_principal_count,
                    '总次数': total_count
                }

        return period_probabilities

    def calculate_overall_stats(self, all_results):
        """计算整体统计数据"""
        any_period_count = 0
        all_periods_count = 0
        total_simulations = len(all_results)
        total_periods_used = 0
        total_principal_used = 0

        for result in all_results:
            # 检查是否有任意一期使用本金
            if result['使用本金'].any():
                any_period_count += 1

            # 检查是否所有期都使用本金
            if result['使用本金'].all():
                all_periods_count += 1

            # 统计使用本金的期数
            periods_used = result['使用本金'].sum()
            total_periods_used += periods_used

            # 统计本金使用金额
            max_principal_used = result['本金使用金额'].max()
            total_principal_used += max_principal_used

        return {
            'any_period_prob': any_period_count / total_simulations,
            'all_periods_prob': all_periods_count / total_simulations,
            'avg_periods': total_periods_used / total_simulations,
            'avg_principal_used': total_principal_used / total_simulations
        }

    def generate_comparison_report(self, fund_results, benchmark_results, fund_probs, benchmark_probs):
        """生成对比分析报告"""
        print("\n" + "="*80)
        print("综合定期支付机制概率分析对比报告")
        print("="*80)

        print(f"\n支付机制：")
        print(f"  支付基础：十年期国债收益率 + 1%")
        print(f"  持有期：{self.holding_period}个月")
        print(f"  支付开始：持有期满后的首月月末")
        print(f"  支付频率：每月末")
        print(f"  分析期间：{self.analysis_period}个月")
        print(f"  分析方式：按日滚动测算近{self.lookback_years}年")

        print(f"\n产品对比：")
        print(f"  单只基金：{self.fund_name} ({self.fund_code})")
        print(f"  基准组合：多资产配置组合")

        if fund_probs and benchmark_probs:
            print(f"\n各期使用本金概率对比：")
            print("-" * 70)
            print(f"{'期数':>4} {'基金概率':>10} {'基准概率':>10} {'差异':>10} {'改善幅度':>10}")
            print("-" * 70)

            for period in range(1, self.analysis_period + 1):
                if period in fund_probs and period in benchmark_probs:
                    fund_prob = fund_probs[period]['概率']
                    benchmark_prob = benchmark_probs[period]['概率']
                    diff = fund_prob - benchmark_prob
                    improvement = (diff / fund_prob * 100) if fund_prob > 0 else 0

                    print(f"{period:>4} {fund_prob:>9.2%} {benchmark_prob:>9.2%} {diff:>9.2%} {improvement:>9.1f}%")

            # 计算整体统计对比
            fund_stats = self.calculate_overall_stats(fund_results)
            benchmark_stats = self.calculate_overall_stats(benchmark_results)

            print(f"\n整体统计对比：")
            print("-" * 70)
            print(f"{'指标':>20} {'基金':>12} {'基准':>12} {'改善幅度':>12}")
            print("-" * 70)

            metrics = [
                ('任意一期使用本金', 'any_period_prob'),
                ('所有期都使用本金', 'all_periods_prob'),
                ('平均使用本金期数', 'avg_periods'),
                ('平均本金使用金额', 'avg_principal_used')
            ]

            for name, key in metrics:
                fund_val = fund_stats[key]
                benchmark_val = benchmark_stats[key]

                if key == 'avg_principal_used':
                    improvement = ((fund_val - benchmark_val) / fund_val * 100) if fund_val > 0 else 0
                    print(f"{name:>20} {fund_val:>9.2f}元 {benchmark_val:>9.2f}元 {improvement:>9.1f}%")
                elif key == 'avg_periods':
                    improvement = ((fund_val - benchmark_val) / fund_val * 100) if fund_val > 0 else 0
                    print(f"{name:>20} {fund_val:>9.1f}期 {benchmark_val:>9.1f}期 {improvement:>9.1f}%")
                else:
                    improvement = ((fund_val - benchmark_val) / fund_val * 100) if fund_val > 0 else 0
                    print(f"{name:>20} {fund_val:>11.2%} {benchmark_val:>11.2%} {improvement:>9.1f}%")

        print("\n" + "="*80)

    def plot_comparison_results(self, fund_probs, benchmark_probs, save_path=None):
        """绘制对比分析图表"""
        if not fund_probs or not benchmark_probs:
            print("没有足够数据进行对比绘图")
            return

        periods = list(range(1, self.analysis_period + 1))
        fund_probabilities = [fund_probs.get(p, {}).get('概率', 0) for p in periods]
        benchmark_probabilities = [benchmark_probs.get(p, {}).get('概率', 0) for p in periods]

        # 创建对比图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 子图1：各期概率对比
        x = np.arange(len(periods))
        width = 0.35

        bars1 = ax1.bar(x - width/2, fund_probabilities, width, label='单只基金', color='lightcoral', alpha=0.7)
        bars2 = ax1.bar(x + width/2, benchmark_probabilities, width, label='基准组合', color='lightblue', alpha=0.7)

        ax1.set_xlabel('支付期数')
        ax1.set_ylabel('使用本金概率')
        ax1.set_title('各期使用本金概率对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(periods)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 子图2：概率差异
        prob_diff = [f - b for f, b in zip(fund_probabilities, benchmark_probabilities)]
        bars3 = ax2.bar(periods, prob_diff, color='orange', alpha=0.7)
        ax2.set_xlabel('支付期数')
        ax2.set_ylabel('概率差异 (基金 - 基准)')
        ax2.set_title('使用本金概率差异')
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # 子图3：累积概率对比
        fund_cumulative = []
        benchmark_cumulative = []

        for i in range(len(fund_probabilities)):
            fund_prob_none = 1
            benchmark_prob_none = 1
            for j in range(i+1):
                fund_prob_none *= (1 - fund_probabilities[j])
                benchmark_prob_none *= (1 - benchmark_probabilities[j])
            fund_cumulative.append(1 - fund_prob_none)
            benchmark_cumulative.append(1 - benchmark_prob_none)

        ax3.plot(periods, fund_cumulative, marker='o', linewidth=2, label='单只基金', color='red')
        ax3.plot(periods, benchmark_cumulative, marker='s', linewidth=2, label='基准组合', color='blue')
        ax3.set_xlabel('支付期数')
        ax3.set_ylabel('累积概率')
        ax3.set_title('至少一期使用本金的累积概率对比')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 子图4：改善幅度
        improvement = [(f - b) / f * 100 if f > 0 else 0 for f, b in zip(fund_probabilities, benchmark_probabilities)]
        bars4 = ax4.bar(periods, improvement, color='green', alpha=0.7)
        ax4.set_xlabel('支付期数')
        ax4.set_ylabel('风险改善幅度 (%)')
        ax4.set_title('基准组合相对基金的风险改善')
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"对比图表已保存至：{save_path}")
            plt.close()
        else:
            plt.show()

    def save_detailed_results(self, fund_results, benchmark_results, fund_probs, benchmark_probs, filename=None):
        """保存详细结果到Excel文件"""
        if filename is None:
            filename = f"综合定期支付分析结果_{dt.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 基金概率汇总表
            if fund_probs:
                fund_prob_df = pd.DataFrame.from_dict(fund_probs, orient='index')
                fund_prob_df.index.name = '支付期数'
                fund_prob_df.to_excel(writer, sheet_name='基金概率汇总')

            # 基准组合概率汇总表
            if benchmark_probs:
                benchmark_prob_df = pd.DataFrame.from_dict(benchmark_probs, orient='index')
                benchmark_prob_df.index.name = '支付期数'
                benchmark_prob_df.to_excel(writer, sheet_name='基准概率汇总')

            # 对比分析表
            if fund_probs and benchmark_probs:
                comparison_data = []
                for period in range(1, self.analysis_period + 1):
                    if period in fund_probs and period in benchmark_probs:
                        fund_prob = fund_probs[period]['概率']
                        benchmark_prob = benchmark_probs[period]['概率']
                        diff = fund_prob - benchmark_prob
                        improvement = (diff / fund_prob * 100) if fund_prob > 0 else 0

                        comparison_data.append({
                            '支付期数': period,
                            '基金概率': fund_prob,
                            '基准概率': benchmark_prob,
                            '概率差异': diff,
                            '改善幅度(%)': improvement
                        })

                comparison_df = pd.DataFrame(comparison_data)
                comparison_df.to_excel(writer, sheet_name='概率对比分析', index=False)

            # 整体统计对比
            if fund_results and benchmark_results:
                fund_stats = self.calculate_overall_stats(fund_results)
                benchmark_stats = self.calculate_overall_stats(benchmark_results)

                stats_comparison = []
                metrics = [
                    ('任意一期使用本金概率', 'any_period_prob'),
                    ('所有期都使用本金概率', 'all_periods_prob'),
                    ('平均使用本金期数', 'avg_periods'),
                    ('平均本金使用金额', 'avg_principal_used')
                ]

                for name, key in metrics:
                    fund_val = fund_stats[key]
                    benchmark_val = benchmark_stats[key]
                    improvement = ((fund_val - benchmark_val) / fund_val * 100) if fund_val > 0 else 0

                    stats_comparison.append({
                        '指标': name,
                        '基金': fund_val,
                        '基准': benchmark_val,
                        '改善幅度(%)': improvement
                    })

                stats_df = pd.DataFrame(stats_comparison)
                stats_df.to_excel(writer, sheet_name='整体统计对比', index=False)

            # 参数设置
            params_df = pd.DataFrame({
                '参数': ['基金代码', '基金名称', '十年期国债代码', '额外利率',
                        '持有期(月)', '分析期(月)', '回测年数'],
                '值': [self.fund_code, self.fund_name, self.treasury_10y_code,
                      f'{self.additional_rate:.2%}', self.holding_period,
                      self.analysis_period, self.lookback_years]
            })
            params_df.to_excel(writer, sheet_name='参数设置', index=False)

        print(f"详细结果已保存至：{filename}")
        return filename

def main():
    """主函数 - 综合定期支付分析"""
    print("综合定期支付机制概率分析")
    print("="*60)
    print("注意：本程序仅使用Wind数据库的真实数据进行分析")
    print("支付基础：十年期国债收益率 + 1%")

    try:
        # 创建分析实例
        analyzer = ComprehensivePaymentAnalysis()

        # 设置分析期间（近15年）
        end_date = dt.datetime.now().strftime('%Y-%m-%d')
        start_date = (dt.datetime.now() - relativedelta(years=analyzer.lookback_years)).strftime('%Y-%m-%d')

        print(f"\n数据获取期间：{start_date} 至 {end_date}")

        # 获取十年期国债收益率数据
        treasury_yield_data = analyzer.get_treasury_yield_data(start_date, end_date)

        # 获取基金数据
        print(f"\n开始分析单只基金：{analyzer.fund_name}")
        fund_nav_data = analyzer.get_fund_data(analyzer.fund_setup_date, end_date)

        # 获取基准组合数据
        print(f"\n开始分析基准组合...")
        benchmark_price_data = analyzer.get_benchmark_data(start_date, end_date)
        benchmark_nav_data = analyzer.calculate_benchmark_nav(benchmark_price_data)

        # 进行基金分析
        print(f"\n开始基金的按日滚动分析...")
        fund_results = analyzer.rolling_analysis(fund_nav_data, treasury_yield_data, "单只基金")
        fund_probs = analyzer.analyze_probability(fund_results, "单只基金")

        # 进行基准组合分析
        print(f"\n开始基准组合的按日滚动分析...")
        benchmark_results = analyzer.rolling_analysis(benchmark_nav_data, treasury_yield_data, "基准组合")
        benchmark_probs = analyzer.analyze_probability(benchmark_results, "基准组合")

        # 生成对比报告
        analyzer.generate_comparison_report(fund_results, benchmark_results, fund_probs, benchmark_probs)

        # 绘制对比图表
        chart_path = f"综合定期支付对比分析图_{dt.datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        analyzer.plot_comparison_results(fund_probs, benchmark_probs, save_path=chart_path)

        # 保存详细结果
        excel_path = analyzer.save_detailed_results(fund_results, benchmark_results, fund_probs, benchmark_probs)

        print(f"\n✅ 综合分析完成！")
        print(f"📊 对比图表：{chart_path}")
        print(f"📋 详细结果：{excel_path}")
        print(f"📈 数据来源：Wind数据库真实数据")
        print(f"💰 支付基础：十年期国债收益率 + 1%")

    except RuntimeError as e:
        print(f"\n❌ 程序执行失败：{e}")
        print("\n解决方案：")
        print("1. 确保已安装WindPy：pip install WindPy")
        print("2. 确保Wind终端已登录且账户有效")
        print("3. 检查网络连接")
        print("4. 验证基金和指数代码是否正确")
        return

    except Exception as e:
        print(f"\n❌ 程序执行过程中发生未知错误：{e}")
        print("请检查程序代码或联系技术支持")
        return

if __name__ == "__main__":
    main()
