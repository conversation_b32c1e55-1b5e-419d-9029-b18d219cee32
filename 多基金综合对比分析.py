# -*- coding: utf-8 -*-
"""
多基金综合定期支付机制概率分析
同时分析多只基金和基准组合的定期支付概率，并进行对比

⚠️ 重要说明：本程序仅使用Wind数据库的真实数据进行分析

基准组合构成：
- 10% * 中证红利指数
- 5% * 黄金
- 5% * 港股高股息指数
- 10% * 标普500指数
- 70% * 中证纯债债基指数

对比基金：
- 工银瑞信双利债券A - 485111.OF
- 工银瑞信四季收益债券A - 000184.OF
- 招商产业债券A - 217022.OF
- 安信稳健增值混合A - 450005.OF

支付机制：
- 支付比例：(同期十年期国债收益率+1%)/12
- 起始支付日期：持有期满6个月后的首月月末
- 支付频率：每月末支付
- 分析期间：随后12个月
- 测算方式：按日滚动测算近15年数据

数据要求：
- 必须安装WindPy并有有效的Wind账户
- 需要网络连接以获取实时数据

作者：AI Assistant
日期：2025年1月27日
版本：4.0 (多基金对比版本)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import datetime as dt
from dateutil.relativedelta import relativedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['figure.figsize'] = (18, 14)

# 导入Wind API - 仅支持真实数据
try:
    from WindPy import *
    # 启动Wind API连接
    result = w.start()
    if result.ErrorCode == 0:
        print("✅ Wind API 连接成功")
    else:
        print(f"❌ Wind API 连接失败，错误码：{result.ErrorCode}")
        w = None
except ImportError:
    print("❌ 未安装WindPy库，无法获取真实数据")
    print("请运行：pip install WindPy")
    w = None
except Exception as e:
    print(f"❌ Wind API 初始化失败：{e}")
    w = None

class MultiFundPaymentAnalysis:
    """多基金定期支付分析类"""

    def __init__(self):
        # 基准组合构成
        self.benchmark_components = {
            '中证红利全收益': {'code': 'H00922.CSI', 'weight': 0.10},
            '黄金': {'code': 'AU9999.SGE', 'weight': 0.05},
            '恒生指数': {'code': 'HSI.HI', 'weight': 0.05},
            '标普500指数': {'code': 'SPX.GI', 'weight': 0.10},
            '中债总指数': {'code': 'CBA00101.CS', 'weight': 0.70}
        }

        # 多只基金信息
        self.funds_info = {
            '工银瑞信双利债券A': {
                'code': '485111.OF',
                'setup_date': '2009-05-27',
                'type': '债券型'
            },
            '工银瑞信四季收益债券A': {
                'code': '000184.OF',
                'setup_date': '2013-06-05',
                'type': '债券型'
            },
            '招商产业债券A': {
                'code': '217022.OF',
                'setup_date': '2012-03-20',
                'type': '债券型'
            },
            '安信稳健增值混合A': {
                'code': '450005.OF',
                'setup_date': '2008-04-23',
                'type': '混合型'
            }
        }

        self.holding_period = 6  # 持有期6个月
        self.analysis_period = 12  # 分析期12个月
        self.lookback_years = 15  # 回测15年

        # 十年期国债收益率代码
        self.treasury_10y_code = 'M1004261.WI'  # 中国十年期国债收益率
        self.default_treasury_rate = 0.03  # 默认国债收益率3%（当数据不可用时使用）
        self.additional_rate = 0.01  # 额外1%

        print(f"多基金综合定期支付机制概率分析")
        print(f"="*70)
        print(f"基准组合构成：")
        for name, info in self.benchmark_components.items():
            print(f"  {name}: {info['weight']:.0%} ({info['code']})")
        print(f"\n对比基金：")
        for name, info in self.funds_info.items():
            print(f"  {name}: {info['code']} ({info['type']})")
        print(f"\n支付基础：十年期国债收益率 + 1%")
        print(f"回测期间：近{self.lookback_years}年")

    def get_treasury_yield_data(self, start_date, end_date):
        """获取十年期国债收益率数据"""
        if w is None:
            raise RuntimeError("Wind API未连接，无法获取真实数据")

        try:
            print("正在获取十年期国债收益率数据...")

            result = w.wsd(self.treasury_10y_code, "close", start_date, end_date, usedf=True)

            if isinstance(result, tuple) and len(result) >= 2:
                error_info = result[0]
                if hasattr(error_info, 'ErrorCode') and error_info.ErrorCode != 0:
                    raise RuntimeError(f"Wind API返回错误：错误码 {error_info.ErrorCode}")
                yield_data = result[1]
            else:
                raise RuntimeError("Wind API返回格式异常")

            if yield_data.empty:
                raise RuntimeError("十年期国债收益率数据为空")

            # 确保索引是DatetimeIndex
            if not isinstance(yield_data.index, pd.DatetimeIndex):
                yield_data.index = pd.to_datetime(yield_data.index)

            yield_data.columns = ['十年期国债收益率']

            # 检查数据质量
            if yield_data['十年期国债收益率'].isna().all():
                print("   警告：十年期国债收益率数据全部为空，使用默认值")
                # 创建默认收益率数据
                yield_data['十年期国债收益率'] = self.default_treasury_rate
            else:
                # 将百分比转换为小数（如果需要）
                valid_data = yield_data['十年期国债收益率'].dropna()
                if len(valid_data) > 0 and valid_data.mean() > 1:
                    yield_data['十年期国债收益率'] = yield_data['十年期国债收益率'] / 100

                # 前向填充缺失值
                yield_data = yield_data.fillna(method='ffill')

                # 如果仍有缺失值，使用默认值
                if yield_data['十年期国债收益率'].isna().any():
                    yield_data['十年期国债收益率'] = yield_data['十年期国债收益率'].fillna(self.default_treasury_rate)

            print(f"✅ 十年期国债收益率数据获取成功")
            print(f"   数据期间：{yield_data.index[0].strftime('%Y-%m-%d')} 至 {yield_data.index[-1].strftime('%Y-%m-%d')}")
            print(f"   平均收益率：{yield_data['十年期国债收益率'].mean():.2%}")

            return yield_data

        except Exception as e:
            raise RuntimeError(f"获取十年期国债收益率数据失败：{e}")

    def get_fund_data(self, fund_name, fund_info, start_date, end_date):
        """获取单只基金净值数据"""
        if w is None:
            raise RuntimeError("Wind API未连接，无法获取真实数据")

        try:
            print(f"正在获取{fund_name}净值数据...")

            result = w.wsd(fund_info['code'], "NAV_adj", start_date, end_date, usedf=True)

            if isinstance(result, tuple) and len(result) >= 2:
                error_info = result[0]
                if hasattr(error_info, 'ErrorCode') and error_info.ErrorCode != 0:
                    print(f"   警告：{fund_name}数据获取失败，错误码 {error_info.ErrorCode}")
                    return None
                nav_data = result[1]
            else:
                print(f"   警告：{fund_name}数据返回格式异常")
                return None

            if nav_data.empty:
                print(f"   警告：{fund_name}净值数据为空")
                return None

            # 确保索引是DatetimeIndex
            if not isinstance(nav_data.index, pd.DatetimeIndex):
                nav_data.index = pd.to_datetime(nav_data.index)

            nav_data.columns = ['净值']
            nav_data = nav_data.dropna()

            print(f"   ✅ {fund_name}数据获取成功")
            print(f"      数据期间：{nav_data.index[0].strftime('%Y-%m-%d')} 至 {nav_data.index[-1].strftime('%Y-%m-%d')}")
            print(f"      数据点数：{len(nav_data)}个")

            return nav_data

        except Exception as e:
            print(f"   ❌ 获取{fund_name}数据失败：{e}")
            return None

    def get_benchmark_data(self, start_date, end_date):
        """获取基准组合数据"""
        if w is None:
            raise RuntimeError("Wind API未连接，无法获取真实数据")

        try:
            print("正在获取基准组合数据...")

            all_data = {}

            for name, info in self.benchmark_components.items():
                print(f"  正在获取 {name} ({info['code']}) 数据...")

                # 根据不同指数类型选择合适的字段
                if 'AU9999' in info['code']:  # 黄金
                    field = "close"
                elif '.HI' in info['code']:  # 港股指数
                    field = "close"
                elif '.GI' in info['code']:  # 国际指数
                    field = "close"
                else:  # A股指数
                    field = "close"

                result = w.wsd(info['code'], field, start_date, end_date, usedf=True)

                if isinstance(result, tuple) and len(result) >= 2:
                    error_info = result[0]
                    if hasattr(error_info, 'ErrorCode') and error_info.ErrorCode != 0:
                        print(f"    警告：{name} 数据获取失败，错误码 {error_info.ErrorCode}")
                        continue
                    data = result[1]
                else:
                    print(f"    警告：{name} 数据返回格式异常")
                    continue

                if data.empty:
                    print(f"    警告：{name} 数据为空")
                    continue

                # 确保索引是DatetimeIndex
                if not isinstance(data.index, pd.DatetimeIndex):
                    data.index = pd.to_datetime(data.index)

                data.columns = [name]
                all_data[name] = data[name]
                print(f"    ✅ {name} 数据获取成功，{len(data)}个数据点")

            if not all_data:
                raise RuntimeError("所有指数数据获取失败")

            # 合并所有数据
            combined_data = pd.DataFrame(all_data)

            # 删除缺失值过多的日期
            min_required_data = max(3, len(self.benchmark_components) * 0.6)
            combined_data = combined_data.dropna(thresh=min_required_data)

            if combined_data.empty:
                raise RuntimeError("合并后的数据为空")

            # 前向填充缺失值
            combined_data = combined_data.fillna(method='ffill')
            combined_data = combined_data.dropna()

            if combined_data.empty:
                raise RuntimeError("填充缺失值后数据为空")

            print(f"✅ 基准组合数据获取完成")
            print(f"   数据期间：{combined_data.index[0].strftime('%Y-%m-%d')} 至 {combined_data.index[-1].strftime('%Y-%m-%d')}")
            print(f"   数据点数量：{len(combined_data)}个交易日")

            return combined_data

        except Exception as e:
            raise RuntimeError(f"获取基准组合数据失败：{e}")

    def calculate_benchmark_nav(self, price_data):
        """计算基准组合净值"""
        print("正在计算基准组合净值...")

        # 计算各指数的日收益率
        returns_data = price_data.pct_change().fillna(0)

        # 计算加权组合收益率
        weighted_returns = pd.Series(0, index=returns_data.index)

        for name, info in self.benchmark_components.items():
            if name in returns_data.columns:
                weighted_returns += returns_data[name] * info['weight']

        # 计算累积净值（以1为起始）
        nav_series = (1 + weighted_returns).cumprod()
        nav_data = pd.DataFrame({'净值': nav_series})

        print(f"✅ 基准组合净值计算完成")
        print(f"   起始净值：{nav_data.iloc[0]['净值']:.4f}")
        print(f"   最终净值：{nav_data.iloc[-1]['净值']:.4f}")
        print(f"   累计收益率：{(nav_data.iloc[-1]['净值'] / nav_data.iloc[0]['净值'] - 1):.2%}")

        return nav_data

    def get_payment_rate(self, date, treasury_yield_data):
        """获取指定日期的支付率"""
        # 找到最接近的国债收益率数据
        available_dates = treasury_yield_data.index[treasury_yield_data.index <= date]
        if len(available_dates) == 0:
            treasury_rate = treasury_yield_data.iloc[0]['十年期国债收益率']
        else:
            current_date = available_dates[-1]
            treasury_rate = treasury_yield_data.loc[current_date]['十年期国债收益率']

        # 计算支付率：十年期国债收益率 + 1%
        annual_payment_rate = treasury_rate + self.additional_rate
        monthly_payment_rate = annual_payment_rate / 12

        return annual_payment_rate, monthly_payment_rate

    def calculate_payment_schedule(self, purchase_date):
        """计算支付时间表"""
        # 持有期满6个月后的首月月末
        holding_end = purchase_date + relativedelta(months=self.holding_period)
        first_payment_date = holding_end + relativedelta(day=31)

        # 生成12个月的支付日期
        payment_dates = []
        current_date = first_payment_date

        for _ in range(self.analysis_period):
            payment_dates.append(current_date)
            current_date = current_date + relativedelta(months=1, day=31)

        return payment_dates

    def simulate_payment_scenario(self, nav_data, treasury_yield_data, purchase_date, product_name, initial_investment=10000):
        """模拟单次投资的支付场景"""
        payment_dates = self.calculate_payment_schedule(purchase_date)

        # 获取购买日净值
        purchase_nav = nav_data.loc[nav_data.index >= purchase_date].iloc[0]['净值']
        shares = initial_investment / purchase_nav

        results = []
        cumulative_payment = 0

        for i, payment_date in enumerate(payment_dates):
            # 找到最接近支付日期的净值
            available_dates = nav_data.index[nav_data.index <= payment_date]
            if len(available_dates) == 0:
                continue

            current_date = available_dates[-1]
            current_nav = nav_data.loc[current_date]['净值']

            # 计算当前市值
            current_value = shares * current_nav
            cumulative_return = current_value - initial_investment

            # 获取当期支付率
            annual_rate, monthly_rate = self.get_payment_rate(current_date, treasury_yield_data)

            # 计算应支付金额
            payment_amount = initial_investment * monthly_rate
            cumulative_payment += payment_amount

            # 判断是否使用本金
            use_principal = cumulative_payment > cumulative_return

            results.append({
                '支付期数': i + 1,
                '支付日期': payment_date,
                '净值日期': current_date,
                '当前净值': current_nav,
                '当前市值': current_value,
                '累积收益': cumulative_return,
                '年化支付率': annual_rate,
                '月支付率': monthly_rate,
                '当期支付': payment_amount,
                '累积支付': cumulative_payment,
                '使用本金': use_principal,
                '本金使用金额': max(0, cumulative_payment - cumulative_return),
                '产品类型': product_name
            })

        return pd.DataFrame(results)

    def rolling_analysis(self, nav_data, treasury_yield_data, product_name):
        """按日滚动分析"""
        print(f"开始{product_name}的按日滚动分析...")

        # 计算最小需要的数据长度
        min_required_days = 18 * 30 + 60

        # 找到所有可能的投资起点日期
        possible_start_dates = []
        for date in nav_data.index:
            future_dates = nav_data.index[nav_data.index > date]
            if len(future_dates) >= min_required_days:
                possible_start_dates.append(date)

        if len(possible_start_dates) == 0:
            print(f"  ❌ {product_name}没有足够的历史数据进行完整分析")
            return []

        print(f"  有效投资起点数量：{len(possible_start_dates)}")
        print(f"  分析期间：{possible_start_dates[0].strftime('%Y-%m-%d')} 至 {possible_start_dates[-1].strftime('%Y-%m-%d')}")

        # 按日滚动分析每个投资起点
        all_results = []
        progress_step = max(1, len(possible_start_dates) // 10)

        for i, start_date in enumerate(possible_start_dates):
            if (i + 1) % progress_step == 0 or i == len(possible_start_dates) - 1:
                print(f"  完成分析：{i + 1}/{len(possible_start_dates)} ({(i+1)/len(possible_start_dates)*100:.1f}%)")

            try:
                scenario_result = self.simulate_payment_scenario(nav_data, treasury_yield_data, start_date, product_name)
                if not scenario_result.empty:
                    scenario_result['投资起点'] = start_date
                    all_results.append(scenario_result)
            except Exception:
                continue

        print(f"  ✅ {product_name}滚动分析完成，有效分析次数：{len(all_results)}")
        return all_results

    def analyze_probability(self, all_results, product_name):
        """分析使用本金的概率"""
        if not all_results:
            print(f"{product_name}没有有效的分析结果")
            return None

        # 统计每个支付期使用本金的概率
        period_probabilities = {}

        for period in range(1, self.analysis_period + 1):
            use_principal_count = 0
            total_count = 0

            for result in all_results:
                period_data = result[result['支付期数'] == period]
                if not period_data.empty:
                    total_count += 1
                    if period_data.iloc[0]['使用本金']:
                        use_principal_count += 1

            if total_count > 0:
                probability = use_principal_count / total_count
                period_probabilities[period] = {
                    '概率': probability,
                    '使用本金次数': use_principal_count,
                    '总次数': total_count
                }

        return period_probabilities

    def calculate_overall_stats(self, all_results):
        """计算整体统计数据"""
        if not all_results:
            return {}

        any_period_count = 0
        all_periods_count = 0
        total_simulations = len(all_results)
        total_periods_used = 0
        total_principal_used = 0

        for result in all_results:
            # 检查是否有任意一期使用本金
            if result['使用本金'].any():
                any_period_count += 1

            # 检查是否所有期都使用本金
            if result['使用本金'].all():
                all_periods_count += 1

            # 统计使用本金的期数
            periods_used = result['使用本金'].sum()
            total_periods_used += periods_used

            # 统计本金使用金额
            max_principal_used = result['本金使用金额'].max()
            total_principal_used += max_principal_used

        return {
            'any_period_prob': any_period_count / total_simulations,
            'all_periods_prob': all_periods_count / total_simulations,
            'avg_periods': total_periods_used / total_simulations,
            'avg_principal_used': total_principal_used / total_simulations
        }

    def generate_multi_fund_report(self, all_fund_results, all_fund_probs, benchmark_results, benchmark_probs):
        """生成多基金对比分析报告"""
        print("\n" + "="*80)
        print("多基金综合定期支付机制概率分析对比报告")
        print("="*80)

        print(f"\n支付机制：")
        print(f"  支付基础：十年期国债收益率 + 1%")
        print(f"  持有期：{self.holding_period}个月")
        print(f"  支付开始：持有期满后的首月月末")
        print(f"  支付频率：每月末")
        print(f"  分析期间：{self.analysis_period}个月")
        print(f"  分析方式：按日滚动测算近{self.lookback_years}年")

        print(f"\n产品对比：")
        for fund_name in self.funds_info.keys():
            fund_type = self.funds_info[fund_name]['type']
            print(f"  {fund_name}: {fund_type}")
        print(f"  基准组合：多资产配置组合")

        # 整体统计对比
        print(f"\n整体统计对比：")
        print("-" * 100)
        print(f"{'产品名称':>20} {'任意一期使用本金':>15} {'所有期都使用本金':>15} {'平均使用期数':>12} {'平均使用金额':>12}")
        print("-" * 100)

        # 基准组合统计
        benchmark_stats = self.calculate_overall_stats(benchmark_results)
        print(f"{'基准组合':>20} {benchmark_stats.get('any_period_prob', 0):>14.2%} {benchmark_stats.get('all_periods_prob', 0):>14.2%} {benchmark_stats.get('avg_periods', 0):>9.1f}期 {benchmark_stats.get('avg_principal_used', 0):>9.2f}元")

        # 各基金统计
        for fund_name, fund_results in all_fund_results.items():
            if fund_results:
                fund_stats = self.calculate_overall_stats(fund_results)
                print(f"{fund_name:>20} {fund_stats.get('any_period_prob', 0):>14.2%} {fund_stats.get('all_periods_prob', 0):>14.2%} {fund_stats.get('avg_periods', 0):>9.1f}期 {fund_stats.get('avg_principal_used', 0):>9.2f}元")

        print("\n" + "="*80)

    def plot_nav_comparison(self, benchmark_nav_data, all_fund_nav_data, save_path=None):
        """绘制净值走势对比图"""
        print("正在生成净值走势对比图...")

        # 找到最晚的基金成立日期作为统一起点
        latest_start_date = None
        valid_funds = {}

        for fund_name, nav_data in all_fund_nav_data.items():
            if nav_data is not None and not nav_data.empty:
                fund_start = nav_data.index[0]
                if latest_start_date is None or fund_start > latest_start_date:
                    latest_start_date = fund_start
                valid_funds[fund_name] = nav_data

        print(f"统一起点日期：{latest_start_date.strftime('%Y-%m-%d')}")

        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))

        # 子图1：所有产品净值走势对比（统一起点，归一化）
        # 基准组合从统一起点开始
        benchmark_aligned = benchmark_nav_data[benchmark_nav_data.index >= latest_start_date]
        if not benchmark_aligned.empty:
            benchmark_normalized = benchmark_aligned['净值'] / benchmark_aligned['净值'].iloc[0]
            ax1.plot(benchmark_aligned.index, benchmark_normalized,
                    linewidth=3, label='基准组合', color='red', alpha=0.8)

        # 各基金从统一起点开始
        colors = ['blue', 'green', 'orange', 'purple', 'brown']
        for i, (fund_name, nav_data) in enumerate(valid_funds.items()):
            nav_aligned = nav_data[nav_data.index >= latest_start_date]
            if not nav_aligned.empty:
                nav_normalized = nav_aligned['净值'] / nav_aligned['净值'].iloc[0]
                ax1.plot(nav_aligned.index, nav_normalized,
                        linewidth=2, label=fund_name, color=colors[i % len(colors)], alpha=0.7)

        ax1.set_title(f'净值走势对比（统一起点：{latest_start_date.strftime("%Y-%m-%d")}，归一化）', fontsize=14, fontweight='bold')
        ax1.set_xlabel('日期')
        ax1.set_ylabel('相对净值（起点=1）')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 子图2：近3年净值走势对比（统一起点，归一化）
        three_years_ago = pd.Timestamp.now() - pd.DateOffset(years=3)
        # 确保近期起点不早于统一起点
        recent_start = max(latest_start_date, three_years_ago)

        # 基准组合近期归一化
        recent_benchmark = benchmark_nav_data[benchmark_nav_data.index >= recent_start]
        if not recent_benchmark.empty:
            recent_benchmark_normalized = recent_benchmark['净值'] / recent_benchmark['净值'].iloc[0]
            ax2.plot(recent_benchmark.index, recent_benchmark_normalized,
                    linewidth=3, label='基准组合', color='red', alpha=0.8)

        # 各基金近期归一化
        for i, (fund_name, nav_data) in enumerate(valid_funds.items()):
            recent_nav = nav_data[nav_data.index >= recent_start]
            if not recent_nav.empty:
                recent_nav_normalized = recent_nav['净值'] / recent_nav['净值'].iloc[0]
                ax2.plot(recent_nav.index, recent_nav_normalized,
                        linewidth=2, label=fund_name, color=colors[i % len(colors)], alpha=0.7)

        ax2.set_title(f'净值走势对比（近期：{recent_start.strftime("%Y-%m-%d")}起，归一化）', fontsize=14, fontweight='bold')
        ax2.set_xlabel('日期')
        ax2.set_ylabel('相对净值（起点=1）')
        ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax2.grid(True, alpha=0.3)

        # 子图3：累计收益率对比（统一起点）
        # 计算各产品从统一起点的累计收益率
        returns_data = []

        # 基准组合收益率（统一起点）
        if not benchmark_aligned.empty:
            benchmark_return = (benchmark_aligned.iloc[-1]['净值'] / benchmark_aligned.iloc[0]['净值'] - 1) * 100
            returns_data.append({'产品': '基准组合', '累计收益率': benchmark_return})

        # 各基金收益率（统一起点）
        for fund_name, nav_data in valid_funds.items():
            nav_aligned = nav_data[nav_data.index >= latest_start_date]
            if not nav_aligned.empty:
                fund_return = (nav_aligned.iloc[-1]['净值'] / nav_aligned.iloc[0]['净值'] - 1) * 100
                returns_data.append({'产品': fund_name, '累计收益率': fund_return})

        if returns_data:
            returns_df = pd.DataFrame(returns_data)
            bars = ax3.bar(range(len(returns_df)), returns_df['累计收益率'],
                          color=['red'] + colors[:len(returns_df)-1], alpha=0.7)
            ax3.set_title(f'累计收益率对比（统一起点：{latest_start_date.strftime("%Y-%m-%d")}）', fontsize=14, fontweight='bold')
            ax3.set_xlabel('产品')
            ax3.set_ylabel('累计收益率 (%)')
            ax3.set_xticks(range(len(returns_df)))
            ax3.set_xticklabels([name[:10] + '...' if len(name) > 10 else name for name in returns_df['产品']],
                               rotation=45, ha='right')
            ax3.grid(True, alpha=0.3)

            # 添加数值标签
            for bar, return_val in zip(bars, returns_df['累计收益率']):
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + (max(returns_df['累计收益率']) * 0.01),
                        f'{return_val:.1f}%', ha='center', va='bottom', fontsize=9)

        # 子图4：年化收益率对比（统一起点）
        # 计算年化收益率
        annual_returns_data = []

        # 基准组合年化收益率（统一起点）
        if not benchmark_aligned.empty:
            days = (benchmark_aligned.index[-1] - benchmark_aligned.index[0]).days
            years = days / 365.25
            if years > 0:
                benchmark_annual = ((benchmark_aligned.iloc[-1]['净值'] / benchmark_aligned.iloc[0]['净值']) ** (1/years) - 1) * 100
                annual_returns_data.append({'产品': '基准组合', '年化收益率': benchmark_annual})

        # 各基金年化收益率（统一起点）
        for fund_name, nav_data in valid_funds.items():
            nav_aligned = nav_data[nav_data.index >= latest_start_date]
            if not nav_aligned.empty:
                days = (nav_aligned.index[-1] - nav_aligned.index[0]).days
                years = days / 365.25
                if years > 0:
                    fund_annual = ((nav_aligned.iloc[-1]['净值'] / nav_aligned.iloc[0]['净值']) ** (1/years) - 1) * 100
                    annual_returns_data.append({'产品': fund_name, '年化收益率': fund_annual})

        if annual_returns_data:
            annual_df = pd.DataFrame(annual_returns_data)
            bars = ax4.bar(range(len(annual_df)), annual_df['年化收益率'],
                          color=['red'] + colors[:len(annual_df)-1], alpha=0.7)
            ax4.set_title(f'年化收益率对比（统一起点：{latest_start_date.strftime("%Y-%m-%d")}）', fontsize=14, fontweight='bold')
            ax4.set_xlabel('产品')
            ax4.set_ylabel('年化收益率 (%)')
            ax4.set_xticks(range(len(annual_df)))
            ax4.set_xticklabels([name[:10] + '...' if len(name) > 10 else name for name in annual_df['产品']],
                               rotation=45, ha='right')
            ax4.grid(True, alpha=0.3)

            # 添加数值标签
            for bar, return_val in zip(bars, annual_df['年化收益率']):
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height + (max(annual_df['年化收益率']) * 0.01),
                        f'{return_val:.1f}%', ha='center', va='bottom', fontsize=9)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"净值走势对比图已保存至：{save_path}")
            plt.close()
        else:
            plt.show()

    def plot_risk_comparison(self, all_fund_probs, benchmark_probs, save_path=None):
        """绘制风险指标对比图"""
        print("正在生成风险指标对比图...")

        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))

        # 准备数据
        products = ['基准组合'] + list(all_fund_probs.keys())
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']

        # 子图1：各期使用本金概率对比
        periods = list(range(1, self.analysis_period + 1))

        # 基准组合概率
        if benchmark_probs:
            benchmark_probabilities = [benchmark_probs.get(p, {}).get('概率', 0) for p in periods]
            ax1.plot(periods, benchmark_probabilities, marker='o', linewidth=3,
                    label='基准组合', color='red', markersize=6)

        # 各基金概率
        for i, (fund_name, fund_probs) in enumerate(all_fund_probs.items()):
            if fund_probs:
                fund_probabilities = [fund_probs.get(p, {}).get('概率', 0) for p in periods]
                ax1.plot(periods, fund_probabilities, marker='s', linewidth=2,
                        label=fund_name, color=colors[(i+1) % len(colors)], markersize=4)

        ax1.set_title('各期使用本金概率对比', fontsize=14, fontweight='bold')
        ax1.set_xlabel('支付期数')
        ax1.set_ylabel('使用本金概率')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 1)

        # 子图2：整体风险指标对比
        risk_metrics = []

        # 基准组合统计
        if benchmark_probs:
            benchmark_results = []  # 这里需要传入实际的results数据
            # 为了简化，我们只显示概率数据
            any_period_prob = max([benchmark_probs.get(p, {}).get('概率', 0) for p in periods])
            risk_metrics.append({'产品': '基准组合', '最高期概率': any_period_prob})

        # 各基金统计
        for fund_name, fund_probs in all_fund_probs.items():
            if fund_probs:
                any_period_prob = max([fund_probs.get(p, {}).get('概率', 0) for p in periods])
                risk_metrics.append({'产品': fund_name, '最高期概率': any_period_prob})

        if risk_metrics:
            risk_df = pd.DataFrame(risk_metrics)
            bars = ax2.bar(range(len(risk_df)), risk_df['最高期概率'],
                          color=colors[:len(risk_df)], alpha=0.7)
            ax2.set_title('最高期使用本金概率对比', fontsize=14, fontweight='bold')
            ax2.set_xlabel('产品')
            ax2.set_ylabel('最高期概率')
            ax2.set_xticks(range(len(risk_df)))
            ax2.set_xticklabels([name[:10] + '...' if len(name) > 10 else name for name in risk_df['产品']],
                               rotation=45, ha='right')
            ax2.grid(True, alpha=0.3)

            # 添加数值标签
            for bar, prob in zip(bars, risk_df['最高期概率']):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{prob:.1%}', ha='center', va='bottom', fontsize=9)

        # 子图3：前期vs后期风险对比
        early_periods = periods[:6]  # 前6期
        late_periods = periods[6:]   # 后6期

        early_late_data = []

        # 基准组合
        if benchmark_probs:
            early_avg = np.mean([benchmark_probs.get(p, {}).get('概率', 0) for p in early_periods])
            late_avg = np.mean([benchmark_probs.get(p, {}).get('概率', 0) for p in late_periods])
            early_late_data.append({'产品': '基准组合', '前期平均': early_avg, '后期平均': late_avg})

        # 各基金
        for fund_name, fund_probs in all_fund_probs.items():
            if fund_probs:
                early_avg = np.mean([fund_probs.get(p, {}).get('概率', 0) for p in early_periods])
                late_avg = np.mean([fund_probs.get(p, {}).get('概率', 0) for p in late_periods])
                early_late_data.append({'产品': fund_name, '前期平均': early_avg, '后期平均': late_avg})

        if early_late_data:
            early_late_df = pd.DataFrame(early_late_data)
            x = np.arange(len(early_late_df))
            width = 0.35

            bars1 = ax3.bar(x - width/2, early_late_df['前期平均'], width,
                           label='前期平均(1-6期)', alpha=0.7, color='lightblue')
            bars2 = ax3.bar(x + width/2, early_late_df['后期平均'], width,
                           label='后期平均(7-12期)', alpha=0.7, color='lightcoral')

            ax3.set_title('前期vs后期风险对比', fontsize=14, fontweight='bold')
            ax3.set_xlabel('产品')
            ax3.set_ylabel('平均使用本金概率')
            ax3.set_xticks(x)
            ax3.set_xticklabels([name[:10] + '...' if len(name) > 10 else name for name in early_late_df['产品']],
                               rotation=45, ha='right')
            ax3.legend()
            ax3.grid(True, alpha=0.3)

        # 子图4：风险趋势分析
        # 计算各产品的风险趋势（后期相对前期的变化）
        trend_data = []

        for i, row in enumerate(early_late_data):
            if row['前期平均'] > 0:
                trend = (row['后期平均'] - row['前期平均']) / row['前期平均'] * 100
            else:
                trend = 0
            trend_data.append({'产品': row['产品'], '风险趋势': trend})

        if trend_data:
            trend_df = pd.DataFrame(trend_data)
            bars = ax4.bar(range(len(trend_df)), trend_df['风险趋势'],
                          color=['green' if x < 0 else 'red' for x in trend_df['风险趋势']], alpha=0.7)
            ax4.set_title('风险趋势分析（后期相对前期变化）', fontsize=14, fontweight='bold')
            ax4.set_xlabel('产品')
            ax4.set_ylabel('风险变化 (%)')
            ax4.set_xticks(range(len(trend_df)))
            ax4.set_xticklabels([name[:10] + '...' if len(name) > 10 else name for name in trend_df['产品']],
                               rotation=45, ha='right')
            ax4.grid(True, alpha=0.3)
            ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)

            # 添加数值标签
            for bar, trend in zip(bars, trend_df['风险趋势']):
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2.,
                        height + (5 if height >= 0 else -10),
                        f'{trend:.1f}%', ha='center',
                        va='bottom' if height >= 0 else 'top', fontsize=9)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"风险指标对比图已保存至：{save_path}")
            plt.close()
        else:
            plt.show()

def main():
    """主函数 - 多基金综合定期支付分析"""
    print("多基金综合定期支付机制概率分析")
    print("="*70)
    print("注意：本程序仅使用Wind数据库的真实数据进行分析")
    print("支付基础：十年期国债收益率 + 1%")

    try:
        # 创建分析实例
        analyzer = MultiFundPaymentAnalysis()

        # 设置分析期间（近15年）
        end_date = dt.datetime.now().strftime('%Y-%m-%d')
        start_date = (dt.datetime.now() - relativedelta(years=analyzer.lookback_years)).strftime('%Y-%m-%d')

        print(f"\n数据获取期间：{start_date} 至 {end_date}")

        # 获取十年期国债收益率数据
        treasury_yield_data = analyzer.get_treasury_yield_data(start_date, end_date)

        # 获取基准组合数据
        print(f"\n开始分析基准组合...")
        benchmark_price_data = analyzer.get_benchmark_data(start_date, end_date)
        benchmark_nav_data = analyzer.calculate_benchmark_nav(benchmark_price_data)

        # 进行基准组合分析
        print(f"\n开始基准组合的按日滚动分析...")
        benchmark_results = analyzer.rolling_analysis(benchmark_nav_data, treasury_yield_data, "基准组合")
        benchmark_probs = analyzer.analyze_probability(benchmark_results, "基准组合")

        # 分析所有基金
        all_fund_results = {}
        all_fund_probs = {}
        all_fund_nav_data = {}

        for fund_name, fund_info in analyzer.funds_info.items():
            print(f"\n开始分析{fund_name}...")

            # 获取基金数据
            fund_nav_data = analyzer.get_fund_data(fund_name, fund_info, fund_info['setup_date'], end_date)

            if fund_nav_data is not None:
                # 保存净值数据用于绘图
                all_fund_nav_data[fund_name] = fund_nav_data

                # 进行基金分析
                fund_results = analyzer.rolling_analysis(fund_nav_data, treasury_yield_data, fund_name)
                fund_probs = analyzer.analyze_probability(fund_results, fund_name)

                all_fund_results[fund_name] = fund_results
                all_fund_probs[fund_name] = fund_probs
            else:
                print(f"  ❌ {fund_name}数据获取失败，跳过分析")
                all_fund_results[fund_name] = []
                all_fund_probs[fund_name] = None
                all_fund_nav_data[fund_name] = None

        # 生成综合对比报告
        analyzer.generate_multi_fund_report(all_fund_results, all_fund_probs, benchmark_results, benchmark_probs)

        # 生成净值走势对比图
        nav_chart_path = f"多基金净值走势对比图_{dt.datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        analyzer.plot_nav_comparison(benchmark_nav_data, all_fund_nav_data, save_path=nav_chart_path)

        # 生成风险指标对比图
        risk_chart_path = f"多基金风险指标对比图_{dt.datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        analyzer.plot_risk_comparison(all_fund_probs, benchmark_probs, save_path=risk_chart_path)

        # 保存详细结果
        filename = f"多基金综合分析结果_{dt.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 基准组合概率汇总表
            if benchmark_probs:
                benchmark_prob_df = pd.DataFrame.from_dict(benchmark_probs, orient='index')
                benchmark_prob_df.index.name = '支付期数'
                benchmark_prob_df.to_excel(writer, sheet_name='基准组合概率')

            # 各基金概率汇总表
            for fund_name, fund_probs in all_fund_probs.items():
                if fund_probs:
                    fund_prob_df = pd.DataFrame.from_dict(fund_probs, orient='index')
                    fund_prob_df.index.name = '支付期数'
                    # 处理sheet名称长度限制
                    sheet_name = fund_name[:20] + "概率" if len(fund_name) > 20 else fund_name + "概率"
                    fund_prob_df.to_excel(writer, sheet_name=sheet_name)

            # 整体统计对比
            stats_data = []

            # 基准组合统计
            benchmark_stats = analyzer.calculate_overall_stats(benchmark_results)
            stats_data.append({
                '产品名称': '基准组合',
                '产品类型': '多资产配置',
                '任意一期使用本金概率': benchmark_stats.get('any_period_prob', 0),
                '所有期都使用本金概率': benchmark_stats.get('all_periods_prob', 0),
                '平均使用本金期数': benchmark_stats.get('avg_periods', 0),
                '平均本金使用金额': benchmark_stats.get('avg_principal_used', 0)
            })

            # 各基金统计
            for fund_name, fund_results in all_fund_results.items():
                if fund_results:
                    fund_stats = analyzer.calculate_overall_stats(fund_results)
                    fund_type = analyzer.funds_info[fund_name]['type']
                    stats_data.append({
                        '产品名称': fund_name,
                        '产品类型': fund_type,
                        '任意一期使用本金概率': fund_stats.get('any_period_prob', 0),
                        '所有期都使用本金概率': fund_stats.get('all_periods_prob', 0),
                        '平均使用本金期数': fund_stats.get('avg_periods', 0),
                        '平均本金使用金额': fund_stats.get('avg_principal_used', 0)
                    })

            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='整体统计对比', index=False)

        print(f"\n✅ 多基金综合分析完成！")
        print(f"📊 净值走势图：{nav_chart_path}")
        print(f"📈 风险对比图：{risk_chart_path}")
        print(f"📋 详细结果：{filename}")
        print(f"📈 数据来源：Wind数据库真实数据")
        print(f"💰 支付基础：十年期国债收益率 + 1%")
        print(f"🎯 分析产品：{len(all_fund_nav_data)}只基金 + 1个基准组合")

    except RuntimeError as e:
        print(f"\n❌ 程序执行失败：{e}")
        print("\n解决方案：")
        print("1. 确保已安装WindPy：pip install WindPy")
        print("2. 确保Wind终端已登录且账户有效")
        print("3. 检查网络连接")
        print("4. 验证基金和指数代码是否正确")
        return

    except Exception as e:
        print(f"\n❌ 程序执行过程中发生未知错误：{e}")
        print("请检查程序代码或联系技术支持")
        return

if __name__ == "__main__":
    main()
