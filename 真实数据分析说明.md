# 工银价值稳健基金定期支付机制概率分析 - 真实数据版本

## ⚠️ 重要说明

**本程序仅使用Wind数据库的真实基金净值数据进行分析，不使用任何模拟数据。**

## 前置要求

### 1. 软件要求
- Python 3.7+
- WindPy库：`pip install WindPy`
- 其他依赖：pandas, numpy, matplotlib, openpyxl

### 2. 数据要求
- **必须有有效的Wind账户**
- **Wind终端必须已登录**
- **需要稳定的网络连接**
- 基金代码：013300.OF (工银价值稳健6个月持有混合FOF)

### 3. 权限要求
- Wind账户需要有基金数据查询权限
- 能够访问历史净值数据

## 使用方法

### 运行程序
```bash
python 工银价值稳健定期支付概率分析.py
```

### 预期输出
如果一切正常，您将看到：
```
✅ Wind API 连接成功
工银价值稳健基金定期支付机制概率分析
==================================================
注意：本程序仅使用Wind数据库的真实基金数据进行分析

基金代码：013300.OF
基金名称：工银价值稳健6个月持有混合(FOF)
年化支付率：3.50%
月支付率：0.2917%

数据获取期间：2021-11-09 至 2025-01-27
正在从Wind数据库获取基金净值数据...
正在获取基金成立以来的完整数据...
成功获取基金真实数据，数据期间：2021-11-09 至 2025-01-27
完整数据期间：2021-11-09 至 2025-01-27
数据点数量：863个交易日
基金数据获取完成，共863个交易日

开始基于真实数据的蒙特卡洛模拟...
开始蒙特卡洛模拟，模拟次数：500
有效购买日期数量：323
完成模拟：100/323
完成模拟：200/323
完成模拟：300/323
有效模拟次数：323

============================================================
工银价值稳健基金定期支付机制概率分析报告
============================================================
[详细分析结果...]

✅ 分析完成！基于真实数据的分析结果：
📊 图表文件：工银价值稳健概率分析图_20250127_143022.png
📋 Excel文件：工银价值稳健定期支付分析结果_20250127_143023.xlsx
📈 数据来源：Wind数据库真实基金净值
📅 数据期间：2021-11-09 至 2025-01-27
```

## 常见问题及解决方案

### 1. Wind API连接失败
**错误信息**：
```
❌ Wind API 连接失败，错误码：-40520007
```

**解决方案**：
- 确保Wind终端已启动并登录
- 检查Wind账户是否有效
- 重启Wind终端后再试

### 2. 未安装WindPy
**错误信息**：
```
❌ 未安装WindPy库，无法获取真实数据
请运行：pip install WindPy
```

**解决方案**：
```bash
pip install WindPy
```

### 3. 数据获取失败
**错误信息**：
```
❌ 程序执行失败：获取Wind真实数据失败：Wind API返回错误：[错误信息]
```

**解决方案**：
- 检查基金代码是否正确（013300.OF）
- 确认Wind账户有基金数据查询权限
- 检查网络连接
- 确认Wind服务器状态正常

### 4. 数据量不足
**警告信息**：
```
警告：数据量较少（XXX个交易日），分析结果可能不够稳定
```

**说明**：
- 程序会继续运行，但结果可靠性可能降低
- 建议等待基金运行更长时间后再进行分析

## 数据质量保证

### 1. 数据来源
- **唯一数据源**：Wind数据库
- **数据类型**：基金复权净值（NAV_adj）
- **数据频率**：日频数据
- **数据范围**：基金成立日至当前日期

### 2. 数据验证
程序会自动进行以下验证：
- 检查数据是否为空
- 验证数据格式和类型
- 确保日期索引正确
- 统计有效数据点数量

### 3. 质量控制
- 自动去除缺失值
- 确保数据连续性
- 验证数据合理性范围

## 分析方法说明

### 1. 蒙特卡洛模拟
- **模拟次数**：500次（可调整）
- **购买时点**：随机选择历史交易日
- **模拟期间**：每次18个月（6个月持有期 + 12个月支付期）

### 2. 概率计算
- **各期概率**：每个支付期使用本金的概率
- **累积概率**：至少一期使用本金的概率
- **整体统计**：平均使用期数、平均使用金额等

### 3. 结果输出
- **控制台报告**：实时显示分析进度和结果
- **图表文件**：概率分布可视化（PNG格式）
- **详细结果**：完整数据表格（Excel格式）

## 参数调整

如需调整分析参数，可修改以下代码：

```python
# 在FundPaymentAnalysis类的__init__方法中
self.bank_rate = 0.025  # 大行理财利率
self.additional_rate = 0.01  # 额外利率
self.holding_period = 6  # 持有期(月)
self.analysis_period = 12  # 分析期(月)

# 在main函数中
num_simulations = 500  # 蒙特卡洛模拟次数
```

## 技术支持

如遇到其他问题，请检查：
1. Wind终端版本是否最新
2. WindPy版本是否兼容
3. Python环境是否正确配置
4. 网络防火墙设置

## 免责声明

- 分析结果基于历史数据，不代表未来表现
- 实际投资结果可能因市场环境变化而不同
- 请结合其他分析方法进行综合判断
- 投资有风险，决策需谨慎
