# -*- coding: utf-8 -*-
"""
基准组合定期支付机制概率分析
基于自定义基准组合测算定期支付时使用本金的概率

⚠️ 重要说明：本程序仅使用Wind数据库的真实指数数据进行分析

基准组合构成：
- 10% * 中证红利指数
- 5% * 黄金
- 5% * 港股高股息指数
- 10% * 标普500指数
- 70% * 中证纯债债基指数

支付机制：
- 支付比例：(大行理财利率+1%)/12
- 起始支付日期：持有期满6个月后的首月月末
- 支付频率：每月末支付
- 分析期间：随后12个月
- 测算方式：按日滚动测算近15年数据

数据要求：
- 必须安装WindPy并有有效的Wind账户
- 需要网络连接以获取实时数据

作者：AI Assistant
日期：2025年1月27日
版本：1.0 (基准组合分析版本)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import datetime as dt
from dateutil.relativedelta import relativedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['figure.figsize'] = (14, 10)

# 导入Wind API - 仅支持真实数据
try:
    from WindPy import *
    # 启动Wind API连接
    result = w.start()
    if result.ErrorCode == 0:
        print("✅ Wind API 连接成功")
    else:
        print(f"❌ Wind API 连接失败，错误码：{result.ErrorCode}")
        w = None
except ImportError:
    print("❌ 未安装WindPy库，无法获取真实数据")
    print("请运行：pip install WindPy")
    w = None
except Exception as e:
    print(f"❌ Wind API 初始化失败：{e}")
    w = None

class BenchmarkPaymentAnalysis:
    """基准组合定期支付分析类"""

    def __init__(self):
        # 基准组合构成 - 使用更可靠的指数代码
        self.benchmark_components = {
            '中证红利全收益': {'code': 'H00922.CSI', 'weight': 0.15},
            '黄金': {'code': 'AU9999.SGE', 'weight': 0.05},
            '恒生指数': {'code': 'HSI.HI', 'weight': 0.00},  # 使用恒生指数
            '标普500指数': {'code': 'SPX.GI', 'weight': 0.10},  # 使用国际指数
            '中债总指数': {'code': 'CBA00101.CS', 'weight': 0.70}  # 使用中债总指数
        }

        self.holding_period = 6  # 持有期6个月
        self.analysis_period = 12  # 分析期12个月
        self.lookback_years = 15  # 回测15年

        # 大行理财利率假设（年化）
        self.bank_rate = 0.025  # 2.5%，可根据实际情况调整
        self.additional_rate = 0.01  # 额外1%
        self.total_rate = self.bank_rate + self.additional_rate  # 总支付率
        self.monthly_payment_rate = self.total_rate / 12  # 月支付率

        print(f"基准组合构成：")
        for name, info in self.benchmark_components.items():
            print(f"  {name}: {info['weight']:.0%} ({info['code']})")
        print(f"年化支付率：{self.total_rate:.2%}")
        print(f"月支付率：{self.monthly_payment_rate:.4%}")
        print(f"回测期间：近{self.lookback_years}年")

    def get_benchmark_data(self, start_date, end_date):
        """获取基准组合数据 - 仅使用真实数据"""
        if w is None:
            raise RuntimeError("Wind API未连接，无法获取真实数据。请确保已安装WindPy并有有效的Wind账户。")

        try:
            print("正在从Wind数据库获取基准组合数据...")

            # 获取所有指数数据
            all_data = {}
            codes = [info['code'] for info in self.benchmark_components.values()]
            names = list(self.benchmark_components.keys())

            for i, (name, info) in enumerate(self.benchmark_components.items()):
                print(f"  正在获取 {name} ({info['code']}) 数据...")

                # 根据不同指数类型选择合适的字段
                if 'AU9999' in info['code']:  # 黄金
                    field = "close"
                elif '.HI' in info['code']:  # 港股指数
                    field = "close"
                elif '.GI' in info['code']:  # 国际指数
                    field = "close"
                else:  # A股指数
                    field = "close"

                result = w.wsd(info['code'], field, start_date, end_date, usedf=True)

                # 检查Wind API返回结果
                if isinstance(result, tuple) and len(result) >= 2:
                    error_info = result[0]
                    if hasattr(error_info, 'ErrorCode') and error_info.ErrorCode != 0:
                        print(f"    警告：{name} 数据获取失败，错误码 {error_info.ErrorCode}")
                        continue
                    data = result[1]
                else:
                    print(f"    警告：{name} 数据返回格式异常")
                    continue

                if data.empty:
                    print(f"    警告：{name} 数据为空")
                    continue

                # 确保索引是DatetimeIndex
                if not isinstance(data.index, pd.DatetimeIndex):
                    data.index = pd.to_datetime(data.index)

                data.columns = [name]
                all_data[name] = data[name]
                print(f"    ✅ {name} 数据获取成功，{len(data)}个数据点")

            if not all_data:
                raise RuntimeError("所有指数数据获取失败")

            # 合并所有数据
            combined_data = pd.DataFrame(all_data)

            print(f"   原始合并数据形状：{combined_data.shape}")
            print(f"   缺失值统计：")
            for col in combined_data.columns:
                missing_count = combined_data[col].isna().sum()
                print(f"     {col}: {missing_count}个缺失值")

            # 删除缺失值过多的日期（至少需要3个指数有数据）
            min_required_data = max(3, len(self.benchmark_components) * 0.6)
            combined_data = combined_data.dropna(thresh=min_required_data)

            if combined_data.empty:
                raise RuntimeError("合并后的数据为空，可能是指数代码错误或数据期间不匹配")

            # 前向填充缺失值
            combined_data = combined_data.fillna(method='ffill')

            # 再次检查并删除仍有缺失值的行
            combined_data = combined_data.dropna()

            if combined_data.empty:
                raise RuntimeError("填充缺失值后数据为空")

            print(f"✅ 基准组合数据获取完成")
            print(f"   数据期间：{combined_data.index[0].strftime('%Y-%m-%d')} 至 {combined_data.index[-1].strftime('%Y-%m-%d')}")
            print(f"   数据点数量：{len(combined_data)}个交易日")
            print(f"   包含指数：{list(combined_data.columns)}")

            return combined_data

        except Exception as e:
            raise RuntimeError(f"获取Wind真实数据失败：{e}。请检查网络连接、Wind账户状态和指数代码是否正确。")

    def calculate_benchmark_nav(self, price_data):
        """计算基准组合净值"""
        print("正在计算基准组合净值...")

        # 计算各指数的日收益率
        returns_data = price_data.pct_change().fillna(0)

        # 计算加权组合收益率
        weighted_returns = pd.Series(0, index=returns_data.index)

        for name, info in self.benchmark_components.items():
            if name in returns_data.columns:
                weighted_returns += returns_data[name] * info['weight']
            else:
                print(f"警告：{name} 数据缺失，权重将分配给其他资产")

        # 计算累积净值（以1为起始）
        nav_series = (1 + weighted_returns).cumprod()
        nav_data = pd.DataFrame({'净值': nav_series})

        print(f"✅ 基准组合净值计算完成")
        print(f"   起始净值：{nav_data.iloc[0]['净值']:.4f}")
        print(f"   最终净值：{nav_data.iloc[-1]['净值']:.4f}")
        print(f"   累计收益率：{(nav_data.iloc[-1]['净值'] / nav_data.iloc[0]['净值'] - 1):.2%}")

        return nav_data

    def calculate_payment_schedule(self, purchase_date):
        """计算支付时间表"""
        # 持有期满6个月后的首月月末
        holding_end = purchase_date + relativedelta(months=self.holding_period)
        first_payment_date = holding_end + relativedelta(day=31)  # 当月最后一天

        # 生成12个月的支付日期
        payment_dates = []
        current_date = first_payment_date

        for _ in range(self.analysis_period):
            payment_dates.append(current_date)
            current_date = current_date + relativedelta(months=1, day=31)

        return payment_dates

    def simulate_payment_scenario(self, nav_data, purchase_date, initial_investment=10000):
        """模拟单次投资的支付场景"""
        payment_dates = self.calculate_payment_schedule(purchase_date)

        # 获取购买日净值
        purchase_nav = nav_data.loc[nav_data.index >= purchase_date].iloc[0]['净值']
        shares = initial_investment / purchase_nav  # 持有份额

        results = []
        cumulative_payment = 0
        cumulative_return = 0

        for i, payment_date in enumerate(payment_dates):
            # 找到最接近支付日期的净值
            available_dates = nav_data.index[nav_data.index <= payment_date]
            if len(available_dates) == 0:
                continue

            current_date = available_dates[-1]
            current_nav = nav_data.loc[current_date]['净值']

            # 计算当前市值
            current_value = shares * current_nav

            # 计算累积收益
            cumulative_return = current_value - initial_investment

            # 计算应支付金额
            payment_amount = initial_investment * self.monthly_payment_rate
            cumulative_payment += payment_amount

            # 判断是否使用本金
            use_principal = cumulative_payment > cumulative_return

            results.append({
                '支付期数': i + 1,
                '支付日期': payment_date,
                '净值日期': current_date,
                '当前净值': current_nav,
                '当前市值': current_value,
                '累积收益': cumulative_return,
                '当期支付': payment_amount,
                '累积支付': cumulative_payment,
                '使用本金': use_principal,
                '本金使用金额': max(0, cumulative_payment - cumulative_return)
            })

        return pd.DataFrame(results)

    def rolling_analysis(self, nav_data):
        """按日滚动分析 - 将每一天都作为投资起点"""
        print(f"开始按日滚动分析...")
        print(f"分析逻辑：将过去15年的每一天都作为投资起点，计算持有6个月后12个月的支付情况")

        # 计算最小需要的数据长度：6个月持有期 + 12个月支付期 = 18个月 ≈ 540天
        min_required_days = 18 * 30 + 60  # 18个月 + 60天缓冲

        # 找到所有可能的投资起点日期
        possible_start_dates = []
        for date in nav_data.index:
            # 检查从该日期开始是否有足够的未来数据
            future_dates = nav_data.index[nav_data.index > date]
            if len(future_dates) >= min_required_days:
                possible_start_dates.append(date)

        if len(possible_start_dates) == 0:
            raise RuntimeError("没有足够的历史数据进行完整分析")

        print(f"有效投资起点数量：{len(possible_start_dates)}")
        print(f"分析期间：{possible_start_dates[0].strftime('%Y-%m-%d')} 至 {possible_start_dates[-1].strftime('%Y-%m-%d')}")
        print(f"每个起点分析：持有6个月 + 支付12个月 = 总计18个月")

        # 按日滚动分析每个投资起点
        all_results = []
        progress_step = max(1, len(possible_start_dates) // 20)  # 显示20个进度点

        for i, start_date in enumerate(possible_start_dates):
            if (i + 1) % progress_step == 0 or i == len(possible_start_dates) - 1:
                print(f"完成分析：{i + 1}/{len(possible_start_dates)} ({(i+1)/len(possible_start_dates)*100:.1f}%)")

            try:
                # 对每个起点进行完整的18个月分析
                scenario_result = self.simulate_payment_scenario(nav_data, start_date)
                if not scenario_result.empty:
                    scenario_result['投资起点'] = start_date
                    # 计算持有期结束日期
                    holding_end_date = start_date + relativedelta(months=self.holding_period)
                    scenario_result['持有期结束'] = holding_end_date
                    all_results.append(scenario_result)
            except Exception as e:
                # 如果某个起点分析失败，继续下一个
                continue

        print(f"✅ 滚动分析完成，有效分析次数：{len(all_results)}")
        print(f"每个分析包含：6个月持有期 + 12个月支付期")
        return all_results

    def analyze_probability(self, all_results):
        """分析使用本金的概率"""
        if not all_results:
            print("没有有效的分析结果")
            return None

        print(f"正在分析概率，总样本数：{len(all_results)}")

        # 统计每个支付期使用本金的概率
        period_probabilities = {}

        for period in range(1, self.analysis_period + 1):
            use_principal_count = 0
            total_count = 0

            for result in all_results:
                period_data = result[result['支付期数'] == period]
                if not period_data.empty:
                    total_count += 1
                    if period_data.iloc[0]['使用本金']:
                        use_principal_count += 1

            if total_count > 0:
                probability = use_principal_count / total_count
                period_probabilities[period] = {
                    '概率': probability,
                    '使用本金次数': use_principal_count,
                    '总次数': total_count
                }

        return period_probabilities

    def calculate_overall_stats(self, all_results):
        """计算整体统计数据"""
        any_period_count = 0
        all_periods_count = 0
        total_simulations = len(all_results)
        total_periods_used = 0
        total_principal_used = 0

        for result in all_results:
            # 检查是否有任意一期使用本金
            if result['使用本金'].any():
                any_period_count += 1

            # 检查是否所有期都使用本金
            if result['使用本金'].all():
                all_periods_count += 1

            # 统计使用本金的期数
            periods_used = result['使用本金'].sum()
            total_periods_used += periods_used

            # 统计本金使用金额
            max_principal_used = result['本金使用金额'].max()
            total_principal_used += max_principal_used

        return {
            'any_period_prob': any_period_count / total_simulations,
            'all_periods_prob': all_periods_count / total_simulations,
            'avg_periods': total_periods_used / total_simulations,
            'avg_principal_used': total_principal_used / total_simulations
        }

    def generate_report(self, period_probabilities, all_results):
        """生成分析报告"""
        print("\n" + "="*80)
        print("基准组合定期支付机制概率分析报告")
        print("="*80)

        print(f"\n基准组合构成：")
        for name, info in self.benchmark_components.items():
            print(f"  {name}: {info['weight']:.0%} ({info['code']})")

        print(f"\n支付机制：")
        print(f"  年化支付率：{self.total_rate:.2%} (大行理财利率{self.bank_rate:.2%} + 1%)")
        print(f"  月支付率：{self.monthly_payment_rate:.4%}")
        print(f"  持有期：{self.holding_period}个月")
        print(f"  支付开始：持有期满后的首月月末")
        print(f"  支付频率：每月末")
        print(f"  分析期间：{self.analysis_period}个月")

        print(f"\n分析方法：")
        print(f"  分析方式：按日滚动测算近{self.lookback_years}年")
        print(f"  分析逻辑：将过去{self.lookback_years}年的每一天都作为投资起点")
        print(f"  每个起点：持有{self.holding_period}个月 + 支付{self.analysis_period}个月 = 总计{self.holding_period + self.analysis_period}个月")
        print(f"  样本数量：{len(all_results)}个有效投资起点")

        if period_probabilities:
            print(f"\n各期使用本金概率：")
            print("-" * 50)
            for period, data in period_probabilities.items():
                print(f"第{period:2d}期：{data['概率']:6.2%} ({data['使用本金次数']:4d}/{data['总次数']:4d})")

            # 计算整体统计
            overall_stats = self.calculate_overall_stats(all_results)
            print(f"\n整体统计：")
            print("-" * 50)
            print(f"任意一期使用本金的概率：{overall_stats['any_period_prob']:.2%}")
            print(f"所有期都使用本金的概率：{overall_stats['all_periods_prob']:.2%}")
            print(f"平均使用本金期数：{overall_stats['avg_periods']:.1f}期")
            print(f"平均本金使用金额：{overall_stats['avg_principal_used']:.2f}元")

        print("\n" + "="*80)

        return period_probabilities

    def plot_results(self, period_probabilities, all_results, save_path=None):
        """绘制分析结果图表"""
        if not period_probabilities:
            print("没有数据可供绘图")
            return

        periods = list(period_probabilities.keys())
        probabilities = [period_probabilities[p]['概率'] for p in periods]

        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 子图1：各期使用本金概率
        bars = ax1.bar(periods, probabilities, color='lightcoral', alpha=0.7, edgecolor='darkred')
        ax1.set_xlabel('支付期数')
        ax1.set_ylabel('使用本金概率')
        ax1.set_title('各期使用本金概率分布')
        ax1.set_ylim(0, 1)
        ax1.grid(True, alpha=0.3)

        # 在柱状图上添加数值标签
        for bar, prob in zip(bars, probabilities):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{prob:.1%}', ha='center', va='bottom', fontsize=9)

        # 子图2：累积概率
        cumulative_probs = []
        for i in range(len(probabilities)):
            prob_none = 1
            for j in range(i+1):
                prob_none *= (1 - probabilities[j])
            cumulative_probs.append(1 - prob_none)

        ax2.plot(periods, cumulative_probs, marker='o', linewidth=2, markersize=6, color='steelblue')
        ax2.set_xlabel('支付期数')
        ax2.set_ylabel('累积概率')
        ax2.set_title('至少一期使用本金的累积概率')
        ax2.set_ylim(0, 1)
        ax2.grid(True, alpha=0.3)

        # 子图3：时间序列分析
        if all_results:
            # 修复列名问题
            start_dates = [result['投资起点'].iloc[0] for result in all_results]
            any_use_principal = [result['使用本金'].any() for result in all_results]

            # 按年统计
            df_time = pd.DataFrame({'投资起点': start_dates, '使用本金': any_use_principal})
            df_time['年份'] = pd.to_datetime(df_time['投资起点']).dt.year
            yearly_stats = df_time.groupby('年份')['使用本金'].mean()

            ax3.plot(yearly_stats.index, yearly_stats.values, marker='s', linewidth=2, markersize=6, color='green')
            ax3.set_xlabel('年份')
            ax3.set_ylabel('使用本金概率')
            ax3.set_title('各年度使用本金概率趋势')
            ax3.set_ylim(0, 1)
            ax3.grid(True, alpha=0.3)
            ax3.tick_params(axis='x', rotation=45)

        # 子图4：本金使用金额分布
        if all_results:
            max_principal_used = [result['本金使用金额'].max() for result in all_results]
            ax4.hist(max_principal_used, bins=30, alpha=0.7, color='orange', edgecolor='darkorange')
            ax4.set_xlabel('最大本金使用金额（元）')
            ax4.set_ylabel('频次')
            ax4.set_title('本金使用金额分布')
            ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存至：{save_path}")
            plt.close()
        else:
            plt.show()

    def save_detailed_results(self, all_results, period_probabilities, filename=None):
        """保存详细结果到Excel文件"""
        if filename is None:
            filename = f"基准组合定期支付分析结果_{dt.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 概率汇总表
            if period_probabilities:
                prob_df = pd.DataFrame.from_dict(period_probabilities, orient='index')
                prob_df.index.name = '支付期数'
                prob_df.to_excel(writer, sheet_name='概率汇总')

            # 整体统计
            if all_results:
                overall_stats = self.calculate_overall_stats(all_results)
                stats_df = pd.DataFrame.from_dict(overall_stats, orient='index', columns=['数值'])
                stats_df.to_excel(writer, sheet_name='整体统计')

            # 详细分析结果（取前20个样本）
            for i, result in enumerate(all_results[:20]):
                sheet_name = f'样本{i+1}'
                result.to_excel(writer, sheet_name=sheet_name, index=False)

            # 基准组合构成
            benchmark_df = pd.DataFrame.from_dict(self.benchmark_components, orient='index')
            benchmark_df.to_excel(writer, sheet_name='基准组合构成')

            # 参数设置
            params_df = pd.DataFrame({
                '参数': ['大行理财利率', '额外利率', '总支付率', '月支付率',
                        '持有期(月)', '分析期(月)', '回测年数', '分析次数'],
                '值': [f'{self.bank_rate:.2%}', f'{self.additional_rate:.2%}',
                      f'{self.total_rate:.2%}', f'{self.monthly_payment_rate:.4%}',
                      self.holding_period, self.analysis_period,
                      self.lookback_years, len(all_results)]
            })
            params_df.to_excel(writer, sheet_name='参数设置', index=False)

        print(f"详细结果已保存至：{filename}")
        return filename

def main():
    """主函数 - 基准组合定期支付分析"""
    print("基准组合定期支付机制概率分析")
    print("="*60)
    print("注意：本程序仅使用Wind数据库的真实指数数据进行分析")

    try:
        # 创建分析实例
        analyzer = BenchmarkPaymentAnalysis()

        # 设置分析期间（近15年）
        end_date = dt.datetime.now().strftime('%Y-%m-%d')
        start_date = (dt.datetime.now() - relativedelta(years=analyzer.lookback_years)).strftime('%Y-%m-%d')

        print(f"\n数据获取期间：{start_date} 至 {end_date}")

        # 获取基准组合数据
        price_data = analyzer.get_benchmark_data(start_date, end_date)

        # 计算基准组合净值
        nav_data = analyzer.calculate_benchmark_nav(price_data)

        # 验证数据质量
        if len(nav_data) < 1000:  # 至少需要1000个交易日的数据
            print(f"警告：数据量较少（{len(nav_data)}个交易日），分析结果可能不够稳定")

        # 进行按日滚动分析
        print(f"\n开始基于真实数据的按日滚动分析...")
        print(f"分析逻辑：将过去{analyzer.lookback_years}年的每一天都作为投资起点")
        print(f"每个起点分析：持有{analyzer.holding_period}个月 + 支付{analyzer.analysis_period}个月")
        all_results = analyzer.rolling_analysis(nav_data)

        if not all_results:
            print("分析失败，程序退出")
            return

        # 分析概率
        period_probabilities = analyzer.analyze_probability(all_results)

        # 生成报告
        analyzer.generate_report(period_probabilities, all_results)

        # 绘制图表
        chart_path = f"基准组合概率分析图_{dt.datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        analyzer.plot_results(period_probabilities, all_results, save_path=chart_path)

        # 保存详细结果
        excel_path = analyzer.save_detailed_results(all_results, period_probabilities)

        print(f"\n✅ 分析完成！基于真实数据的分析结果：")
        print(f"📊 图表文件：{chart_path}")
        print(f"📋 Excel文件：{excel_path}")
        print(f"📈 数据来源：Wind数据库真实指数数据")
        print(f"📅 数据期间：{nav_data.index[0].strftime('%Y-%m-%d')} 至 {nav_data.index[-1].strftime('%Y-%m-%d')}")
        print(f"🔄 分析方式：按日滚动测算，共{len(all_results)}个样本")

        # 显示基准组合表现
        total_return = (nav_data.iloc[-1]['净值'] / nav_data.iloc[0]['净值'] - 1) * 100
        annual_return = ((nav_data.iloc[-1]['净值'] / nav_data.iloc[0]['净值']) ** (252 / len(nav_data)) - 1) * 100
        print(f"📈 基准组合表现：累计收益{total_return:.2f}%，年化收益{annual_return:.2f}%")

    except RuntimeError as e:
        print(f"\n❌ 程序执行失败：{e}")
        print("\n解决方案：")
        print("1. 确保已安装WindPy：pip install WindPy")
        print("2. 确保Wind终端已登录且账户有效")
        print("3. 检查网络连接")
        print("4. 验证指数代码是否正确")
        return

    except Exception as e:
        print(f"\n❌ 程序执行过程中发生未知错误：{e}")
        print("请检查程序代码或联系技术支持")
        return

if __name__ == "__main__":
    main()
